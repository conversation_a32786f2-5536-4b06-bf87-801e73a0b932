#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB连接测试脚本
验证数据库连接是否正常并进行基本的CRUD操作测试
"""

import sys
import os
from datetime import datetime
import logging

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mongodb_manager import RadiationDataManager
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mongodb_connection():
    """测试MongoDB连接"""
    print("🔍 测试MongoDB连接...")
    print(f"📊 连接信息: {Config.MONGODB_URI}")
    print("-" * 50)
    
    try:
        # 初始化数据库管理器
        db_manager = RadiationDataManager()
        
        # 测试连接
        print("✅ MongoDB连接成功!")
        
        # 获取数据库统计信息
        stats = db_manager.get_statistics()
        print(f"📈 数据库统计:")
        print(f"   - 核技术利用单位: {stats.get('total_units', 0)} 个")
        print(f"   - 放射源记录: {stats.get('total_sources', 0)} 个")
        print(f"   - 已地理编码单位: {stats.get('geocoded_units', 0)} 个")
        
        # 列出所有集合
        collections = db_manager.db.list_collection_names()
        print(f"📚 数据库集合: {', '.join(collections)}")
        
        return True
        
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        print("📋 请检查以下项目:")
        print("   1. MongoDB服务是否已启动")
        print("   2. 连接字符串是否正确")
        print("   3. 数据库权限是否配置正确")
        return False

def test_crud_operations():
    """测试基本的CRUD操作"""
    print("\n🧪 测试基本CRUD操作...")
    print("-" * 50)
    
    try:
        db_manager = RadiationDataManager()
        
        # 测试插入数据
        test_unit = {
            'name': '测试核技术利用单位',
            'license_no': 'TEST001',
            'address': '上海市测试区测试路123号',
            'district': '测试区',
            'social_credit_code': '91310000TEST001',
            'legal_person': '测试负责人',
            'contact_phone': '021-12345678',
            'business_scope': '测试用途',
            'test_record': True  # 标记为测试记录
        }
        
        unit_id = db_manager.insert_unit(test_unit)
        print(f"✅ 插入测试单位成功，ID: {unit_id}")
        
        # 测试插入地理编码
        db_manager.insert_geocode(unit_id, 121.4737, 31.2304, test_unit['address'])
        print("✅ 插入地理编码成功")
        
        # 测试查询数据
        units = db_manager.get_all_units_with_location()
        test_units = [u for u in units if u.get('test_record')]
        print(f"✅ 查询成功，找到 {len(test_units)} 个测试单位")
        
        # 清理测试数据
        db_manager.db[db_manager.collections['units']].delete_many({'test_record': True})
        db_manager.db[db_manager.collections['geocodes']].delete_many({'unit_id': db_manager.db.dereference})
        print("✅ 清理测试数据成功")
        
        return True
        
    except Exception as e:
        print(f"❌ CRUD操作测试失败: {e}")
        return False

def test_web_service():
    """测试Web服务集成"""
    print("\n🌐 测试Web服务集成...")
    print("-" * 50)
    
    try:
        from web_service import create_app
        
        # 创建Flask应用
        app = create_app()
        
        # 测试应用配置
        with app.app_context():
            print("✅ Flask应用创建成功")
            print(f"📡 服务器配置: {Config.WEB_HOST}:{Config.WEB_PORT}")
            print(f"🔧 调试模式: {'开启' if Config.DEBUG else '关闭'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Web服务测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 放射性元素布点可视化系统 - MongoDB集成测试")
    print("=" * 60)
    
    # 测试项目列表
    tests = [
        ("MongoDB连接测试", test_mongodb_connection),
        ("CRUD操作测试", test_crud_operations),
        ("Web服务集成测试", test_web_service)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}发生异常: {e}")
            results[test_name] = False
    
    # 输出测试结果汇总
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(tests)} 项测试通过")
    
    if success_count == len(tests):
        print("🎉 所有测试通过，系统可以正常使用!")
        print("💡 下一步可以:")
        print("   1. 运行数据迁移: python migrate_to_mongodb.py")
        print("   2. 启动Web服务: python start_server.py")
    else:
        print("⚠️ 部分测试失败，请检查配置和环境")
    
    return success_count == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 