#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件分析脚本
分析放射性元素布点系统的三个Excel数据文件
"""

import pandas as pd
import os
import sys

def analyze_excel_file(file_path, file_name):
    """分析单个Excel文件"""
    try:
        print(f"\n{'='*60}")
        print(f"分析文件: {file_name}")
        print(f"{'='*60}")
        
        # 读取Excel文件
        try:
            df = pd.read_excel(file_path)
        except Exception as e:
            # 尝试不同的编码
            df = pd.read_excel(file_path, engine='openpyxl')
        
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        
        print(f"\n列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        print(f"\n数据类型:")
        for col in df.columns:
            print(f"{col}: {df[col].dtype}")
        
        print(f"\n前5行数据:")
        print(df.head().to_string())
        
        print(f"\n数据统计:")
        print(df.describe(include='all').to_string())
        
        # 检查空值
        print(f"\n空值统计:")
        null_counts = df.isnull().sum()
        for col, count in null_counts.items():
            if count > 0:
                print(f"{col}: {count} ({count/len(df)*100:.1f}%)")
        
        # 如果有地址相关的列，显示示例
        address_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in ['地址', 'address', '地点', '位置'])]
        if address_cols:
            print(f"\n地址相关列示例:")
            for col in address_cols:
                print(f"\n{col}:")
                print(df[col].dropna().head(10).to_string())
        
        # 如果有单位名称相关的列，显示示例
        unit_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in ['单位', '公司', '企业', '机构', 'unit', 'company'])]
        if unit_cols:
            print(f"\n单位名称相关列示例:")
            for col in unit_cols:
                print(f"\n{col}:")
                print(df[col].dropna().head(10).to_string())
        
        return df
        
    except Exception as e:
        print(f"分析文件 {file_name} 时出错: {str(e)}")
        return None

def main():
    """主函数"""
    base_dir = r"C:\Users\<USER>\Desktop\radiation-map-system"
    
    excel_files = [
        "20250304核技术利用单位基本信息列表to消防.xls",
        "20250314持证单位列表.xls", 
        "20250314放射源明细.xls"
    ]
    
    dataframes = {}
    
    for file_name in excel_files:
        file_path = os.path.join(base_dir, file_name)
        if os.path.exists(file_path):
            df = analyze_excel_file(file_path, file_name)
            if df is not None:
                key = file_name.split('.')[0]  # 去掉扩展名作为key
                dataframes[key] = df
        else:
            print(f"文件不存在: {file_path}")
    
    # 分析表之间的关联关系
    print(f"\n{'='*60}")
    print("表之间关联分析")
    print(f"{'='*60}")
    
    if len(dataframes) >= 2:
        # 查找可能的关联字段
        for i, (name1, df1) in enumerate(dataframes.items()):
            for j, (name2, df2) in enumerate(dataframes.items()):
                if i < j:  # 避免重复比较
                    print(f"\n{name1} vs {name2}:")
                    
                    # 查找相同的列名
                    common_cols = set(df1.columns) & set(df2.columns)
                    if common_cols:
                        print(f"  相同列名: {list(common_cols)}")
                        
                        # 对于相同的列，检查重叠的值
                        for col in common_cols:
                            if col in df1.columns and col in df2.columns:
                                overlap = set(df1[col].dropna()) & set(df2[col].dropna())
                                if overlap:
                                    print(f"    {col}: 有 {len(overlap)} 个重叠值")
                                    if len(overlap) <= 5:
                                        print(f"      示例: {list(overlap)}")
                    
                    # 查找相似的列名（可能是关联字段）
                    similar_cols = []
                    for col1 in df1.columns:
                        for col2 in df2.columns:
                            if ('单位' in col1 and '单位' in col2) or \
                               ('公司' in col1 and '公司' in col2) or \
                               ('名称' in col1 and '名称' in col2):
                                similar_cols.append((col1, col2))
                    
                    if similar_cols:
                        print(f"  相似列名（可能关联）: {similar_cols}")

if __name__ == "__main__":
    # 检查pandas是否安装
    try:
        import pandas as pd
        import openpyxl
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        print("请运行: pip install pandas openpyxl xlrd")
        sys.exit(1)
    
    main()