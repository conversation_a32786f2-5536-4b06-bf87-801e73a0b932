<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上海市放射性元素布点可视化系统</title>
    <script src="https://webapi.amap.com/maps?v=2.0&key=addee20e44549d9a58a43451785008e8&plugin=AMap.Scale,AMap.ToolBar,AMap.MouseTool"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 100px);
        }
        
        .sidebar {
            width: 350px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
        }
        
        .stats-panel {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .filters-panel {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .filter-group {
            margin-bottom: 15px;
        }
        
        .filter-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .filter-select, .filter-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .info-popup {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            max-width: 300px;
            font-size: 13px;
        }
        
        .info-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 15px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
        }
        
        .info-content {
            padding: 15px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 5px;
        }
        
        .info-label {
            width: 80px;
            color: #666;
            font-size: 12px;
        }
        
        .info-value {
            flex: 1;
            color: #333;
            font-weight: 500;
        }
        
        .legend {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-size: 12px;
        }
        
        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 200px;
            }
            
            .map-container {
                height: calc(100vh - 300px);
            }
        }
        
        .category-1 { background-color: #ff4757; }
        .category-2 { background-color: #ff6b35; }
        .category-3 { background-color: #ffa502; }
        .category-4 { background-color: #3742fa; }
        .category-5 { background-color: #2ed573; }
    </style>
</head>
<body>
    <div class="header">
        <h1>上海市放射性元素布点可视化系统</h1>
        <p>Radiation Source Distribution Visualization System in Shanghai</p>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="stats-panel">
                <h3 style="margin-top: 0; color: #333;">数据统计</h3>
                <div class="stat-item">
                    <span class="stat-label">总单位数</span>
                    <span class="stat-value" id="total-units">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总放射源数</span>
                    <span class="stat-value" id="total-sources">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">已显示</span>
                    <span class="stat-value" id="visible-units">-</span>
                </div>
            </div>
            
            <div class="filters-panel">
                <h3 style="margin-top: 0; color: #333;">筛选条件</h3>
                
                <div class="filter-group">
                    <label class="filter-label">所属区县</label>
                    <select class="filter-select" id="district-filter">
                        <option value="">全部区县</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">放射源类别</label>
                    <select class="filter-select" id="category-filter">
                        <option value="">全部类别</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">单位名称搜索</label>
                    <input type="text" class="filter-input" id="name-filter" placeholder="输入单位名称关键词...">
                </div>
                
                <div class="filter-group">
                    <button onclick="resetFilters()" style="width: 100%; padding: 10px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">重置筛选</button>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="loading" id="loading">
                <div>地图加载中...</div>
                <div style="margin-top: 10px; font-size: 12px;">请确保已配置高德地图API Key</div>
            </div>
            
            <div class="legend">
                <div class="legend-title">放射源类别</div>
                <div class="legend-item">
                    <div class="legend-color category-1"></div>
                    <span>Ⅰ类（极高危险源）</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color category-2"></div>
                    <span>Ⅱ类（高危险源）</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color category-3"></div>
                    <span>Ⅲ类（中等危险源）</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color category-4"></div>
                    <span>Ⅳ类（较低危险源）</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color category-5"></div>
                    <span>Ⅴ类（极低危险源）</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let map;
        let allData = [];
        let markers = [];
        let currentData = [];

        // 类别颜色映射
        const categoryColors = {
            'Ⅰ类': '#ff4757',
            'Ⅱ类': '#ff6b35', 
            'Ⅲ类': '#ffa502',
            'Ⅳ类': '#3742fa',
            'Ⅴ类': '#2ed573'
        };

        // 初始化地图
        function initMap() {
            // 检查是否有高德地图API
            if (typeof AMap === 'undefined') {
                document.getElementById('loading').innerHTML = `
                    <div style="color: #ff4757;">地图加载失败</div>
                    <div style="margin-top: 10px; font-size: 12px;">请检查高德地图API Key配置</div>
                    <div style="margin-top: 10px; font-size: 11px;">请参考 API_CONFIG.md 文件配置说明</div>
                `;
                return;
            }

            map = new AMap.Map('map', {
                zoom: 10,
                center: [121.473701, 31.231706], // 上海市中心
                mapStyle: 'amap://styles/light',
                features: ['bg', 'road', 'building'],
                viewMode: '2D'
            });

            // 添加工具栏和比例尺
            map.addControl(new AMap.ToolBar({
                position: 'RB'
            }));
            
            map.addControl(new AMap.Scale({
                position: 'LB'
            }));

            // 地图加载完成后加载数据
            map.on('complete', function() {
                document.getElementById('loading').style.display = 'none';
                loadData();
            });
        }

        // 加载数据
        async function loadData() {
            try {
                const response = await fetch('radiation_map_data_lite.json');
                allData = await response.json();
                currentData = [...allData];
                
                updateStats();
                populateFilters();
                displayMarkers();
                
            } catch (error) {
                console.error('数据加载失败:', error);
                alert('数据加载失败，请确保 radiation_map_data_lite.json 文件存在');
            }
        }

        // 更新统计信息
        function updateStats() {
            const totalSources = allData.reduce((sum, unit) => sum + unit.source_count, 0);
            
            document.getElementById('total-units').textContent = allData.length;
            document.getElementById('total-sources').textContent = totalSources;
            document.getElementById('visible-units').textContent = currentData.length;
        }

        // 填充筛选器选项
        function populateFilters() {
            // 区县筛选
            const districts = [...new Set(allData.map(unit => unit.district))].sort();
            const districtSelect = document.getElementById('district-filter');
            districts.forEach(district => {
                if (district) {
                    const option = document.createElement('option');
                    option.value = district;
                    option.textContent = district;
                    districtSelect.appendChild(option);
                }
            });

            // 放射源类别筛选
            const categories = [...new Set(allData.flatMap(unit => unit.categories))].sort();
            const categorySelect = document.getElementById('category-filter');
            categories.forEach(category => {
                if (category) {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    categorySelect.appendChild(option);
                }
            });
        }

        // 显示标记点
        function displayMarkers() {
            // 清除现有标记
            markers.forEach(marker => map.remove(marker));
            markers = [];

            currentData.forEach(unit => {
                if (unit.longitude && unit.latitude) {
                    // 确定标记颜色（基于主要放射源类别）
                    const mainCategory = unit.categories[0] || 'Ⅴ类';
                    const color = categoryColors[mainCategory] || '#2ed573';

                    const marker = new AMap.Marker({
                        position: [unit.longitude, unit.latitude],
                        icon: new AMap.Icon({
                            size: [12, 12],
                            image: `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12">
                                    <circle cx="6" cy="6" r="5" fill="${color}" stroke="white" stroke-width="1"/>
                                </svg>
                            `)}`,
                            imageSize: [12, 12]
                        }),
                        title: unit.name
                    });

                    // 点击事件
                    marker.on('click', function() {
                        showInfoWindow(unit, marker);
                    });

                    markers.push(marker);
                    map.add(marker);
                }
            });

            updateStats();
        }

        // 显示信息窗口
        function showInfoWindow(unit, marker) {
            const content = `
                <div class="info-popup">
                    <div class="info-header">${unit.name}</div>
                    <div class="info-content">
                        <div class="info-row">
                            <span class="info-label">地址:</span>
                            <span class="info-value">${unit.address || '未知'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">区县:</span>
                            <span class="info-value">${unit.district || '未知'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">法人:</span>
                            <span class="info-value">${unit.legal_person || '未知'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">电话:</span>
                            <span class="info-value">${unit.legal_phone || '未知'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">放射源数:</span>
                            <span class="info-value">${unit.source_count}</span>
                        </div>
                        ${unit.main_nuclides.length > 0 ? `
                        <div class="info-row">
                            <span class="info-label">主要核素:</span>
                            <span class="info-value">${unit.main_nuclides.join(', ')}</span>
                        </div>
                        ` : ''}
                        ${unit.categories.length > 0 ? `
                        <div class="info-row">
                            <span class="info-label">放射源类别:</span>
                            <span class="info-value">${unit.categories.join(', ')}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;

            const infoWindow = new AMap.InfoWindow({
                content: content,
                offset: [0, -30]
            });

            infoWindow.open(map, marker.getPosition());
        }

        // 筛选数据
        function filterData() {
            const districtFilter = document.getElementById('district-filter').value;
            const categoryFilter = document.getElementById('category-filter').value;
            const nameFilter = document.getElementById('name-filter').value.toLowerCase();

            currentData = allData.filter(unit => {
                // 区县筛选
                if (districtFilter && unit.district !== districtFilter) {
                    return false;
                }

                // 类别筛选
                if (categoryFilter && !unit.categories.includes(categoryFilter)) {
                    return false;
                }

                // 名称搜索
                if (nameFilter && !unit.name.toLowerCase().includes(nameFilter)) {
                    return false;
                }

                return true;
            });

            displayMarkers();
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('district-filter').value = '';
            document.getElementById('category-filter').value = '';
            document.getElementById('name-filter').value = '';
            
            currentData = [...allData];
            displayMarkers();
        }

        // 绑定筛选事件
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('district-filter').addEventListener('change', filterData);
            document.getElementById('category-filter').addEventListener('change', filterData);
            document.getElementById('name-filter').addEventListener('input', filterData);
        });

        // 初始化
        initMap();
    </script>
</body>
</html>