# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.venv/
.env
venv/
ENV/
env/
.venv

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 大文件数据 (可选：如果不想提交大文件)
*.json
*.xls
*.xlsx
*.csv

# 操作系统
.DS_Store
Thumbs.db

# Node.js (如果有前端项目)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 临时文件
temp/
tmp/
*.tmp

# 配置文件 (如果包含敏感信息)
config.ini
.env.local
.env.production 