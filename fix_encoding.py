#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复编码问题并重新生成JSON数据
"""

import pandas as pd
import json
import os
import numpy as np

def load_and_fix_data():
    """重新加载数据并修复编码问题"""
    
    base_dir = r"C:\Users\<USER>\Desktop\radiation-map-system"
    
    # 重新加载Excel文件，明确指定编码
    license_info = pd.read_excel(os.path.join(base_dir, "20250314持证单位列表.xls"))
    source_detail = pd.read_excel(os.path.join(base_dir, "20250314放射源明细.xls"))
    
    # 合并数据并创建地图数据结构
    map_data = []
    
    # 处理每个单位
    for idx, unit in license_info.iterrows():
        # 获取该单位的放射源详情
        unit_sources = source_detail[
            (source_detail['所属单位'] == unit['单位名称']) |
            (source_detail['许可证号'] == unit['许可证号'])
        ]
        
        # 格式化地址
        address = str(unit['单位地址']) if pd.notna(unit['单位地址']) else ''
        if address and not address.startswith('上海市'):
            address = f"上海市{unit['所属地市']}{address}" if pd.notna(unit['所属地市']) else f"上海市{address}"
        
        # 构建单位信息
        unit_info = {
            'id': f"unit_{idx + 1}",
            'name': str(unit['单位名称']) if pd.notna(unit['单位名称']) else '',
            'license_no': str(unit['许可证号']) if pd.notna(unit['许可证号']) else '',
            'address': address,
            'district': str(unit['所属地市']) if pd.notna(unit['所属地市']) else '',
            'legal_person': str(unit['法人']) if pd.notna(unit['法人']) else '',
            'legal_phone': str(unit['法人电话']) if pd.notna(unit['法人电话']) else '',
            'approval_agency': str(unit['审批机关']) if pd.notna(unit['审批机关']) else '',
            'license_date': str(unit['当前发证日期']) if pd.notna(unit['当前发证日期']) else '',
            'expire_date': str(unit['有效期至']) if pd.notna(unit['有效期至']) else '',
            'social_credit_code': str(unit['统一社会信用代码']) if pd.notna(unit['统一社会信用代码']) else '',
            
            # 活动范围
            'radiation_source_scope': str(unit['放射源活动种类和范围']) if pd.notna(unit['放射源活动种类和范围']) else '',
            'unsealed_material_scope': str(unit['非密封放射性物质活动种类和范围']) if pd.notna(unit['非密封放射性物质活动种类和范围']) else '',
            'radiation_device_scope': str(unit['射线装置活动种类和范围']) if pd.notna(unit['射线装置活动种类和范围']) else '',
            
            # 放射源详情
            'sources': [],
            'source_count': len(unit_sources),
            
            # 坐标信息（待地理编码）
            'longitude': None,
            'latitude': None,
            'geocoded': False
        }
        
        # 添加放射源详情
        for _, source in unit_sources.iterrows():
            source_info = {
                'source_code': str(source['放射源编码']) if pd.notna(source['放射源编码']) else '',
                'nuclide_name': str(source['核素名称']) if pd.notna(source['核素名称']) else '',
                'country': str(source['国家（地区）']) if pd.notna(source['国家（地区）']) else '',
                'manufacturer': str(source['生产厂家']) if pd.notna(source['生产厂家']) else '',
                'source_category': str(source['放射源类别']) if pd.notna(source['放射源类别']) else '',
                'source_purpose': str(source['放射源用途']) if pd.notna(source['放射源用途']) else '',
                'factory_activity': float(source['出厂活度（Bq）']) if pd.notna(source['出厂活度（Bq）']) else 0,
                'current_activity': float(source['实时活度（Bq）']) if pd.notna(source['实时活度（Bq）']) else 0,
                'factory_date': str(source['出厂日期']) if pd.notna(source['出厂日期']) else '',
                'label': str(source['标号']) if pd.notna(source['标号']) else '',
                'region': str(source['所属区域']) if pd.notna(source['所属区域']) else '',
                'status': str(source['放射源状态']) if pd.notna(source['放射源状态']) else '',
                'current_province': str(source['源现使用省']) if pd.notna(source['源现使用省']) else ''
            }
            unit_info['sources'].append(source_info)
        
        map_data.append(unit_info)
    
    return map_data

def save_fixed_data(map_data):
    """保存修复后的数据"""
    
    # 保存为JSON格式
    with open("radiation_map_data_fixed.json", 'w', encoding='utf-8') as f:
        json.dump(map_data, f, ensure_ascii=False, indent=2)
    
    print(f"修复后的数据已保存到: radiation_map_data_fixed.json")
    print(f"总单位数量: {len(map_data)}")
    
    # 显示前几个单位的信息
    print("\n修复后的样例数据:")
    for i, unit in enumerate(map_data[:3]):
        print(f"\n单位 {i+1}:")
        print(f"  ID: {unit['id']}")
        print(f"  名称: {unit['name']}")
        print(f"  地址: {unit['address']}")
        print(f"  区县: {unit['district']}")
        print(f"  法人: {unit['legal_person']}")
        print(f"  电话: {unit['legal_phone']}")
        print(f"  放射源数量: {unit['source_count']}")
        if unit['sources']:
            source = unit['sources'][0]
            print(f"  主要放射源: {source['nuclide_name']} ({source['source_category']})")

def main():
    """主函数"""
    print("开始修复数据编码...")
    
    # 加载并修复数据
    map_data = load_and_fix_data()
    
    # 保存修复后的数据
    save_fixed_data(map_data)
    
    print("数据编码修复完成！")

if __name__ == "__main__":
    main()