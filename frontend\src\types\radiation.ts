// 放射源详细信息
export interface RadioactiveSourceDetail {
  nuclideName: string;         // 核素名称
  sourceCategory: string;      // 放射源类别
  sourceUsage: string;         // 放射源用途
  sourceStatus: string;        // 放射源状态
  activity?: string;           // 活度
  activityUnit?: string;       // 活度单位
  manufactureDate?: string;    // 制造日期
  serialNumber?: string;       // 序列号
  manufacturer?: string;       // 制造商
}

// 许可证信息
export interface LicenseInfo {
  licenseNumber: string;       // 许可证号
  issueDate: string;          // 发证日期
  validityPeriod: string;     // 有效期
  issuingAuthority?: string;  // 发证机关
  scope?: string;             // 许可范围
}

// 地理坐标
export interface Coordinates {
  longitude: number;          // 经度
  latitude: number;           // 纬度
}

// 统一的放射源信息模型
export interface RadioactiveSource {
  id: string;                 // 唯一标识
  unitName: string;           // 单位名称
  address: string;            // 单位地址
  coordinates?: Coordinates;   // 地理坐标
  
  // 联系信息
  legalPerson: string;        // 法人
  legalPersonPhone: string;   // 法人电话
  contactPerson?: string;     // 联系人
  contactPhone?: string;      // 联系电话
  
  // 放射源详情
  sources: RadioactiveSourceDetail[];
  
  // 许可证信息
  licenseInfo?: LicenseInfo;
  
  // 行政区划信息
  district: string;           // 所属区域
  streetOffice?: string;      // 街道办事处
  community?: string;         // 社区
  
  // 时间信息
  createTime?: string;        // 创建时间
  updateTime: string;         // 更新时间
  lastInspectionTime?: string; // 最后检查时间
  
  // 状态信息
  overallStatus: 'normal' | 'warning' | 'danger'; // 总体状态
  riskLevel: 'low' | 'medium' | 'high';          // 风险等级
  
  // 统计信息
  totalSources: number;       // 放射源总数
  activeSources: number;      // 使用中的放射源数量
  inactiveSources: number;    // 非使用状态的放射源数量
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  total?: number;
  page?: number;
  pageSize?: number;
}

// 搜索和筛选参数
export interface SearchParams {
  keyword?: string;           // 关键词搜索
  district?: string;          // 区域筛选
  sourceStatus?: string;      // 状态筛选
  sourceCategory?: string;    // 类别筛选
  riskLevel?: string;         // 风险等级筛选
  page?: number;             // 分页页码
  pageSize?: number;         // 分页大小
}

// 统计数据类型
export interface StatisticsData {
  totalUnits: number;         // 总单位数
  totalSources: number;       // 总放射源数
  activeUnits: number;        // 活跃单位数
  riskDistribution: {         // 风险分布
    low: number;
    medium: number;
    high: number;
  };
  districtDistribution: Array<{  // 区域分布
    district: string;
    count: number;
    percentage: number;
  }>;
  categoryDistribution: Array<{  // 类别分布
    category: string;
    count: number;
    percentage: number;
  }>;
  statusDistribution: Array<{    // 状态分布
    status: string;
    count: number;
    percentage: number;
  }>;
}

// 地图相关类型
export interface MapConfig {
  center: [number, number];   // 地图中心点
  zoom: number;              // 缩放级别
  mapStyle: string;          // 地图样式
}

export interface MarkerClusterOptions {
  enabled: boolean;          // 是否启用聚合
  gridSize: number;          // 网格大小
  maxZoom: number;           // 最大缩放级别
}

// 导出数据类型
export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf';
  fields: string[];          // 导出字段
  filters?: SearchParams;    // 筛选条件
}

// 数据导入类型
export interface ImportResult {
  success: boolean;
  total: number;             // 总记录数
  processed: number;         // 处理成功数
  errors: Array<{            // 错误记录
    row: number;
    message: string;
    data?: any;
  }>;
  warnings: Array<{          // 警告记录
    row: number;
    message: string;
    data?: any;
  }>;
}