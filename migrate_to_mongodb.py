#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本
将现有的JSON格式数据导入到MongoDB数据库中
"""

import json
import os
import sys
from datetime import datetime
import logging
from typing import Dict, List, Any
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mongodb_manager import RadiationDataManager
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('migration.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def load_json_data(file_path: str) -> List[Dict]:
    """
    加载JSON数据文件
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        解析后的JSON数据列表
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"❌ 文件不存在: {file_path}")
            return []
            
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        logger.info(f"✅ 成功加载JSON文件: {file_path}")
        
        if isinstance(data, list):
            logger.info(f"📋 数据包含 {len(data)} 条记录")
            return data
        else:
            logger.warning(f"⚠️ 数据格式不是列表，将转换为列表: {type(data)}")
            return [data]
        
    except Exception as e:
        logger.error(f"❌ 加载JSON文件失败: {file_path}, 错误: {e}")
        return []

def process_unit_data(unit_data: Dict) -> Dict:
    """
    处理单个单位数据，确保格式正确
    
    Args:
        unit_data: 原始单位数据
        
    Returns:
        处理后的单位数据
    """
    try:
        processed_data = {
            'unit_id': unit_data.get('id', ''),
            'name': unit_data.get('name', ''),
            'license_no': unit_data.get('license_no', ''),
            'address': unit_data.get('address', ''),
            'district': unit_data.get('district', ''),
            'legal_person': unit_data.get('legal_person', ''),
            'legal_phone': unit_data.get('legal_phone', ''),
            'approval_agency': unit_data.get('approval_agency', ''),
            'license_date': unit_data.get('license_date', ''),
            'expire_date': unit_data.get('expire_date', ''),
            'social_credit_code': unit_data.get('social_credit_code', ''),
            'radiation_source_scope': unit_data.get('radiation_source_scope', ''),
            'unsealed_material_scope': unit_data.get('unsealed_material_scope', ''),
            'radiation_device_scope': unit_data.get('radiation_device_scope', ''),
            'source_count': unit_data.get('source_count', 0),
            'longitude': unit_data.get('longitude'),
            'latitude': unit_data.get('latitude'),
            'geocoded': unit_data.get('geocoded', False),
            'sources': unit_data.get('sources', []),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        return processed_data
        
    except Exception as e:
        logger.error(f"❌ 处理单位数据失败: {e}")
        return {}

def migrate_units_data(db_manager: RadiationDataManager, file_path: str) -> bool:
    """
    迁移单位数据到MongoDB
    
    Args:
        db_manager: 数据库管理器
        file_path: JSON文件路径
        
    Returns:
        迁移是否成功
    """
    try:
        logger.info(f"🔄 开始迁移单位数据: {file_path}")
        
        # 加载JSON数据
        units_data = load_json_data(file_path)
        
        if not units_data:
            logger.warning(f"⚠️ 没有找到单位数据: {file_path}")
            return False
        
        # 清空现有数据（可选）
        logger.info("🗑️ 清空现有单位数据...")
        db_manager.clear_collection('units')
        
        # 处理并插入数据
        processed_units = []
        for i, unit in enumerate(units_data):
            processed_unit = process_unit_data(unit)
            if processed_unit:
                processed_units.append(processed_unit)
            
            if (i + 1) % 100 == 0:
                logger.info(f"📊 已处理 {i + 1}/{len(units_data)} 条单位数据")
        
        if processed_units:
            # 批量插入数据
            logger.info(f"💾 插入 {len(processed_units)} 条单位数据到MongoDB...")
            result = db_manager.batch_insert_units(processed_units)
            
            if result:
                logger.info(f"✅ 单位数据迁移成功！共迁移 {len(processed_units)} 条记录")
                return True
            else:
                logger.error("❌ 单位数据迁移失败")
                return False
        else:
            logger.warning("⚠️ 没有有效的单位数据需要迁移")
            return False
            
    except Exception as e:
        logger.error(f"❌ 迁移单位数据失败: {e}")
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("🚀 开始数据迁移...")
    print(f"📊 MongoDB连接: {Config.MONGODB_URI}")
    print("-" * 60)
    
    try:
        # 初始化数据库管理器
        db_manager = RadiationDataManager()
        
        # 定义要迁移的文件
        json_files = [
            'radiation_map_data.json',
            'radiation_map_data_fixed.json', 
            'radiation_map_data_geocoded.json'
        ]
        
        # 寻找存在的文件
        existing_files = []
        for file_path in json_files:
            if os.path.exists(file_path):
                existing_files.append(file_path)
                logger.info(f"✅ 找到数据文件: {file_path}")
        
        if not existing_files:
            logger.error("❌ 没有找到任何JSON数据文件")
            return False
        
        # 选择最优的数据文件（优先选择geocoded版本）
        selected_file = existing_files[0]
        if 'radiation_map_data_geocoded.json' in existing_files:
            selected_file = 'radiation_map_data_geocoded.json'
        elif 'radiation_map_data_fixed.json' in existing_files:
            selected_file = 'radiation_map_data_fixed.json'
        
        logger.info(f"📄 选择数据文件: {selected_file}")
        
        # 迁移单位数据
        success = migrate_units_data(db_manager, selected_file)
        
        if success:
            print("\n" + "="*60)
            print("🎉 数据迁移完成！")
            
            # 显示统计信息
            stats = db_manager.get_statistics()
            print(f"📊 数据库统计:")
            for collection, count in stats.items():
                print(f"   - {collection}: {count} 条记录")
                
            print("="*60)
            return True
        else:
            print("❌ 数据迁移失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 迁移过程发生错误: {e}")
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 