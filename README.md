# 放射性元素上海市布点可视化系统

## 📋 项目概述

本系统用于展示放射性元素在上海市行政区域内的分布情况，提供交互式地图可视化功能，支持详细信息查看和数据管理。

## 🏗️ 系统架构

```
frontend/          # 前端React应用
├── src/
│   ├── components/    # 组件库
│   ├── pages/        # 页面组件  
│   ├── services/     # API服务
│   ├── hooks/        # 自定义Hook
│   └── utils/        # 工具函数
├── public/           # 静态资源
└── package.json

backend/           # 后端Node.js服务
├── src/
│   ├── controllers/  # 控制器
│   ├── models/       # 数据模型
│   ├── routes/       # 路由定义
│   ├── services/     # 业务服务
│   └── utils/        # 工具函数
├── config/           # 配置文件
└── package.json

data-processor/    # 数据处理模块
├── src/
│   ├── parsers/      # Excel解析器
│   ├── geocoder/     # 地理编码
│   └── integrator/   # 数据整合
└── package.json

docs/              # 项目文档
├── api.md            # API文档
├── deployment.md     # 部署文档
└── user-guide.md     # 用户手册
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- MongoDB >= 4.4
- Redis >= 6.0

### 安装依赖
```bash
# 安装前端依赖
cd frontend && npm install

# 安装后端依赖  
cd backend && npm install

# 安装数据处理依赖
cd data-processor && npm install
```

### 配置环境变量
```bash
cp backend/.env.example backend/.env
# 编辑 .env 文件，配置数据库连接和高德地图API密钥
```

### 启动服务
```bash
# 启动后端服务
cd backend && npm run dev

# 启动前端服务
cd frontend && npm run dev
```

## 📊 数据源

系统支持以下三张Excel表格的数据导入：
1. `20250304核技术利用单位基本信息列表to消防.xls`
2. `20250314持证单位列表.xls`  
3. `20250314放射源明细.xls`

数据通过单位名称进行关联整合。

## 🎯 主要功能

- ✅ 上海市行政区域地图展示
- ✅ 放射源点位可视化标注
- ✅ 鼠标悬停信息提示
- ✅ 详细信息弹窗展示
- ✅ 数据筛选和搜索
- ✅ 地理编码和坐标转换
- ✅ 响应式设计适配

## 🛠️ 技术栈

**前端**
- React 18 + TypeScript
- 高德地图API
- Ant Design + Tailwind CSS
- Vite构建工具

**后端**
- Node.js + Express
- MongoDB数据库
- Redis缓存
- Excel文件处理

## 📖 API文档

详见 [API文档](./docs/api.md)

## 🚀 部署指南

详见 [部署文档](./docs/deployment.md)

## 📞 联系方式

如有问题请联系项目维护人员。
