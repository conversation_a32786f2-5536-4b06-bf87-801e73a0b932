#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置文件
包含MongoDB连接信息、API配置和其他系统设置
"""

import os
from typing import Dict, Any

class Config:
    """系统配置类"""
    
    # MongoDB配置
    MONGODB_URI = "mongodb://localhost:27017/Unit_Info"
    MONGODB_DATABASE = "Unit_Info"
    
    # 数据集合名称配置
    COLLECTIONS = {
        'units': 'radiation_units',           # 核技术利用单位基本信息
        'sources': 'radiation_sources',       # 放射源信息
        'devices': 'radiation_devices',       # 射线装置信息
        'licenses': 'radiation_licenses',     # 许可证信息
        'inspections': 'safety_inspections',  # 安全检查信息
        'incidents': 'safety_incidents',      # 安全事件信息
        'locations': 'geo_locations',         # 地理位置信息
        'statistics': 'data_statistics'       # 数据统计信息
    }
    
    # Web服务配置
    WEB_HOST = "localhost"
    WEB_PORT = 8000
    DEBUG = True
    
    # API配置
    API_PREFIX = "/api"
    API_VERSION = "v1"
    
    # 地理编码配置
    GEOCODING_CONFIG = {
        'default_region': '上海市',
        'default_country': '中国',
        'timeout': 10,
        'cache_enabled': True,
        'cache_expiry': 86400  # 24小时
    }
    
    # 数据处理配置
    DATA_CONFIG = {
        'batch_size': 100,
        'max_retry': 3,
        'encoding': 'utf-8',
        'backup_enabled': True,
        'backup_dir': 'backups'
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_enabled': True,
        'file_path': 'logs/radiation_system.log',
        'max_size': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5
    }
    
    # 静态文件配置
    STATIC_CONFIG = {
        'map_template': 'templates/map_template.html',
        'data_export_dir': 'exports',
        'temp_dir': 'temp'
    }
    
    @classmethod
    def get_mongodb_uri(cls) -> str:
        """获取MongoDB连接URI"""
        return os.getenv('MONGODB_URI', cls.MONGODB_URI)
    
    @classmethod
    def get_database_name(cls) -> str:
        """获取数据库名称"""
        return os.getenv('MONGODB_DATABASE', cls.MONGODB_DATABASE)
    
    @classmethod
    def get_web_config(cls) -> Dict[str, Any]:
        """获取Web服务配置"""
        return {
            'host': os.getenv('WEB_HOST', cls.WEB_HOST),
            'port': int(os.getenv('WEB_PORT', cls.WEB_PORT)),
            'debug': os.getenv('DEBUG', str(cls.DEBUG)).lower() == 'true'
        }
    
    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        dirs_to_create = [
            cls.DATA_CONFIG['backup_dir'],
            cls.STATIC_CONFIG['data_export_dir'],
            cls.STATIC_CONFIG['temp_dir'],
            os.path.dirname(cls.LOGGING_CONFIG['file_path'])
        ]
        
        for directory in dirs_to_create:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)

# 开发环境配置
class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    MONGODB_URI = "mongodb://localhost:27017/Unit_Info_Dev"

# 生产环境配置
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    MONGODB_URI = "mongodb://localhost:27017/Unit_Info_Prod"
    
    LOGGING_CONFIG = {
        **Config.LOGGING_CONFIG,
        'level': 'WARNING',
        'file_path': '/var/log/radiation_system/radiation_system.log'
    }

# 测试环境配置
class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    MONGODB_URI = "mongodb://localhost:27017/Unit_Info_Test"
    
    DATA_CONFIG = {
        **Config.DATA_CONFIG,
        'batch_size': 10,
        'backup_enabled': False
    }

# 配置选择映射
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': Config
}

def get_config(env: str = None) -> Config:
    """获取配置对象"""
    if env is None:
        env = os.getenv('FLASK_ENV', 'default')
    
    return config_map.get(env, Config)

# 默认配置实例
default_config = get_config() 