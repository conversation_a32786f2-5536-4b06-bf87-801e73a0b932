#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JSON数据文件结构
"""

import json
import os
from pprint import pprint

def check_json_file(filename):
    """检查JSON文件结构"""
    print(f"\n{'='*50}")
    print(f"📄 检查文件: {filename}")
    print(f"{'='*50}")
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return None
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 文件读取成功")
        print(f"📊 数据类型: {type(data)}")
        
        if isinstance(data, list):
            print(f"📋 数据长度: {len(data)} 条记录")
            if len(data) > 0:
                print(f"🔍 第一条记录的字段:")
                first_item = data[0]
                if isinstance(first_item, dict):
                    for key, value in first_item.items():
                        print(f"   - {key}: {type(value).__name__} = {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
                
                print(f"\n📝 前2条数据样例:")
                pprint(data[:2])
        
        elif isinstance(data, dict):
            print(f"📋 字典类型，包含 {len(data)} 个键")
            print(f"🔑 键列表: {list(data.keys())}")
            
            # 如果是字典，检查每个键的值类型
            for key, value in data.items():
                if isinstance(value, list):
                    print(f"   - {key}: list with {len(value)} items")
                    if len(value) > 0 and isinstance(value[0], dict):
                        print(f"     第一个item的字段: {list(value[0].keys())}")
                else:
                    print(f"   - {key}: {type(value).__name__}")
        
        return data
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 检查JSON数据文件结构")
    
    # 检查所有JSON文件
    json_files = [f for f in os.listdir('.') if f.endswith('.json')]
    
    print(f"📁 发现 {len(json_files)} 个JSON文件:")
    for file in json_files:
        print(f"   - {file}")
    
    # 逐个检查
    for file in json_files:
        data = check_json_file(file)

if __name__ == "__main__":
    main() 