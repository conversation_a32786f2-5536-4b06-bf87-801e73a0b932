<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/radiation-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="放射性元素上海市布点可视化系统" />
    <title>放射性元素分布可视化系统</title>

    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=1.4.15&key=YOUR_AMAP_KEY&plugin=AMap.Scale,AMap.ToolBar,AMap.DistrictSearch"></script>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <style>
      /* 基础样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                     'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 
                     'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
                     'Noto Color Emoji';
        line-height: 1.6;
        color: #333;
        background-color: #f5f5f5;
      }

      #root {
        min-height: 100vh;
      }

      /* 加载动画 */
      .loading-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 20px;
      }

      @keyframes spin {
        to { transform: rotate(360deg); }
      }

      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }

      .loading-subtext {
        font-size: 14px;
        opacity: 0.8;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        body {
          font-size: 14px;
        }
      }

      /* 自定义滚动条 */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }

      /* 高德地图自定义样式 */
      .amap-container {
        font-family: inherit !important;
      }

      .amap-info-window {
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
      }

      /* 工具提示样式 */
      .custom-tooltip {
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- 初始加载画面 -->
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">放射性元素分布可视化系统</div>
        <div class="loading-subtext">正在加载系统资源...</div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>