#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
放射性元素布点可视化系统 - 主启动脚本
支持MongoDB数据存储和Flask Web服务
"""

import os
import sys
import time
import subprocess
import webbrowser
from datetime import datetime
import json
import platform

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'flask',
        'flask-cors', 
        'pymongo',
        'pandas',
        'requests',
        'geopy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n📦 需要安装以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        
        print(f"\n安装命令:")
        print(f"   pip install {' '.join(missing_packages)}")
        
        # 询问是否自动安装
        response = input("\n是否自动安装缺失的依赖包? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            try:
                print("\n🔧 正在安装依赖包...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
                print("✅ 依赖包安装完成")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ 安装失败: {e}")
                return False
        else:
            return False
    
    return True

def check_mongodb_connection():
    """检查MongoDB连接"""
    try:
        from pymongo import MongoClient
        
        print("🔍 检查MongoDB连接...")
        client = MongoClient("mongodb://localhost:27017/Unit_Info", serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.server_info()
        
        # 获取数据库信息
        db = client.get_database("Unit_Info")
        collections = db.list_collection_names()
        
        print(f"  ✅ MongoDB连接成功")
        print(f"  📊 数据库: Unit_Info")
        print(f"  📁 集合数量: {len(collections)}")
        
        if collections:
            print("  📋 现有集合:")
            for collection in collections:
                count = db[collection].count_documents({})
                print(f"     - {collection}: {count} 条记录")
        else:
            print("  ⚠️  暂无数据集合，建议运行数据迁移")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"  ❌ MongoDB连接失败: {e}")
        print("  💡 请确保MongoDB服务已启动并监听在 localhost:27017")
        return False

def check_json_data():
    """检查JSON数据文件是否存在"""
    print("🔍 检查JSON数据文件...")
    
    json_files = [
        'radiation_units_with_geocoding.json',
        'radiation_sources_complete.json',
        'shanghai_radiation_units.json',
        'units_geocoding_results.json'
    ]
    
    existing_files = []
    missing_files = []
    
    for file in json_files:
        if os.path.exists(file):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    size = len(data) if isinstance(data, list) else 1
                    print(f"  ✅ {file} ({size} 条记录)")
                    existing_files.append((file, size))
            except Exception as e:
                print(f"  ⚠️  {file} (读取错误: {e})")
        else:
            print(f"  ❌ {file} - 文件不存在")
            missing_files.append(file)
    
    return existing_files, missing_files

def migrate_data_to_mongodb():
    """数据迁移到MongoDB"""
    print("\n🔄 开始数据迁移到MongoDB...")
    
    try:
        # 检查迁移脚本是否存在
        if not os.path.exists('migrate_to_mongodb.py'):
            print("❌ 迁移脚本 migrate_to_mongodb.py 不存在")
            return False
        
        # 执行迁移脚本
        result = subprocess.run([sys.executable, 'migrate_to_mongodb.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 数据迁移完成")
            print(result.stdout)
            return True
        else:
            print("❌ 数据迁移失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 数据迁移异常: {e}")
        return False

def start_web_service():
    """启动Web服务"""
    print("\n🚀 启动Web服务...")
    
    try:
        # 检查Web服务脚本
        if not os.path.exists('web_service.py'):
            print("❌ Web服务脚本 web_service.py 不存在")
            return False
        
        # 启动服务
        print("🌐 正在启动服务器...")
        subprocess.Popen([sys.executable, 'web_service.py'])
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 测试服务器是否正常
        try:
            import requests
            response = requests.get('http://localhost:8000/api/health', timeout=5)
            if response.status_code == 200:
                print("✅ 服务器启动成功")
                return True
            else:
                print(f"⚠️  服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"⚠️  服务器健康检查失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 启动Web服务失败: {e}")
        return False

def open_browser():
    """打开浏览器"""
    try:
        url = "http://localhost:8000"
        print(f"🌐 正在打开浏览器: {url}")
        webbrowser.open(url)
        return True
    except Exception as e:
        print(f"⚠️  打开浏览器失败: {e}")
        return False

def show_system_info():
    """显示系统信息"""
    print("📋 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   Python版本: {sys.version.split()[0]}")
    print(f"   工作目录: {os.getcwd()}")
    print(f"   启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    print("🏥 上海市放射性元素布点可视化系统")
    print("=" * 60)
    print("🚀 基于MongoDB的数据管理和Flask Web服务")
    print("=" * 60)
    
    # 显示系统信息
    show_system_info()
    print()
    
    # 1. 检查依赖项
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的Python包后重试")
        input("按回车键退出...")
        return
    
    print()
    
    # 2. 检查MongoDB连接
    if not check_mongodb_connection():
        print("\n❌ MongoDB连接失败")
        print("💡 请按以下步骤操作:")
        print("   1. 启动MongoDB服务")
        print("   2. 确保MongoDB监听在 localhost:27017")
        print("   3. 创建数据库 Unit_Info")
        input("按回车键退出...")
        return
    
    print()
    
    # 3. 检查JSON数据文件
    existing_files, missing_files = check_json_data()
    
    # 4. 询问是否需要数据迁移
    if existing_files:
        print(f"\n🔄 检测到 {len(existing_files)} 个JSON数据文件")
        response = input("是否将JSON数据迁移到MongoDB? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            if not migrate_data_to_mongodb():
                print("⚠️  数据迁移失败，但仍可继续启动服务")
        else:
            print("⏭️  跳过数据迁移")
    
    print()
    
    # 5. 启动Web服务
    if not start_web_service():
        print("❌ Web服务启动失败")
        input("按回车键退出...")
        return
    
    # 6. 显示访问信息
    print("\n" + "=" * 60)
    print("✅ 系统启动成功!")
    print("=" * 60)
    print("🌐 访问地址:")
    print("   http://localhost:8000     - 地图页面")
    print("   http://localhost:8000/api/health - 健康检查")
    print()
    print("📋 主要功能:")
    print("   📊 数据统计和可视化")
    print("   🔍 单位搜索和筛选")
    print("   📍 地理位置查询")
    print("   🌐 交互式地图展示")
    print()
    print("💡 使用提示:")
    print("   - 使用Ctrl+C停止服务")
    print("   - 查看API文档: http://localhost:8000/api/")
    print("   - 数据管理可通过API接口进行")
    
    # 7. 打开浏览器
    time.sleep(2)
    open_browser()
    
    # 8. 保持程序运行
    try:
        print("\n⏸️  服务器正在运行中...")
        print("   按Ctrl+C停止服务")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n👋 正在停止服务...")
        print("✅ 服务已停止")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 系统启动异常: {e}")
        input("按回车键退出...")