import axios from 'axios';
import fs from 'fs-extra';
import path from 'path';

/**
 * 地理编码服务
 * 使用高德地图API将地址转换为经纬度坐标
 */
class Geocoder {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://restapi.amap.com/v3/geocode/geo';
    this.cache = new Map(); // 地址缓存
    this.requestCount = 0;
    this.maxRequestsPerSecond = 5; // API限流
    this.requestQueue = [];
    this.processing = false;
  }

  /**
   * 地理编码单个地址
   * @param {string} address - 地址字符串
   * @param {string} city - 城市名称（可选）
   * @returns {Object} 坐标结果
   */
  async geocodeAddress(address, city = '上海市') {
    try {
      // 检查缓存
      const cacheKey = `${city}-${address}`;
      if (this.cache.has(cacheKey)) {
        console.log(`🎯 缓存命中: ${address}`);
        return this.cache.get(cacheKey);
      }

      // 清理和格式化地址
      const cleanAddress = this.cleanAddress(address);
      if (!cleanAddress) {
        throw new Error('地址为空或无效');
      }

      // 构建请求参数
      const params = {
        key: this.apiKey,
        address: cleanAddress,
        city: city,
        output: 'json'
      };

      console.log(`🔍 正在查询地址: ${cleanAddress}`);

      // 发送API请求
      const response = await axios.get(this.baseUrl, {
        params,
        timeout: 10000
      });

      this.requestCount++;

      if (response.data.status !== '1') {
        throw new Error(`地理编码失败: ${response.data.info}`);
      }

      const geocodes = response.data.geocodes;
      if (!geocodes || geocodes.length === 0) {
        throw new Error('未找到匹配的地址');
      }

      // 解析坐标结果
      const result = this.parseGeocodeResult(geocodes[0], address);
      
      // 缓存结果
      this.cache.set(cacheKey, result);
      
      console.log(`✅ 地址解析成功: ${address} -> (${result.coordinates.longitude}, ${result.coordinates.latitude})`);
      
      return result;

    } catch (error) {
      console.error(`❌ 地址解析失败: ${address}`, error.message);
      
      return {
        originalAddress: address,
        coordinates: null,
        success: false,
        error: error.message,
        confidence: 0
      };
    }
  }

  /**
   * 批量地理编码
   * @param {Array} addresses - 地址数组
   * @param {Function} progressCallback - 进度回调函数
   * @returns {Array} 编码结果数组
   */
  async batchGeocode(addresses, progressCallback) {
    console.log(`🚀 开始批量地理编码，共 ${addresses.length} 个地址`);
    
    const results = [];
    const uniqueAddresses = [...new Set(addresses)]; // 去重
    
    console.log(`📍 去重后需要处理 ${uniqueAddresses.length} 个唯一地址`);

    for (let i = 0; i < uniqueAddresses.length; i++) {
      const address = uniqueAddresses[i];
      
      try {
        // API限流控制
        await this.throttleRequest();
        
        const result = await this.geocodeAddress(address);
        results.push(result);
        
        // 调用进度回调
        if (progressCallback) {
          progressCallback({
            current: i + 1,
            total: uniqueAddresses.length,
            percentage: Math.round(((i + 1) / uniqueAddresses.length) * 100),
            address: address,
            success: result.success
          });
        }

        // 每处理50个地址休息一下
        if ((i + 1) % 50 === 0) {
          console.log(`⏸️ 已处理 ${i + 1} 个地址，休息2秒...`);
          await this.sleep(2000);
        }

      } catch (error) {
        console.error(`❌ 处理地址失败: ${address}`, error.message);
        results.push({
          originalAddress: address,
          coordinates: null,
          success: false,
          error: error.message,
          confidence: 0
        });
      }
    }

    console.log(`✅ 批量地理编码完成`);
    console.log(`📊 成功: ${results.filter(r => r.success).length}, 失败: ${results.filter(r => !r.success).length}`);

    return results;
  }

  /**
   * 清理和格式化地址
   * @param {string} address - 原始地址
   * @returns {string} 清理后的地址
   */
  cleanAddress(address) {
    if (!address || typeof address !== 'string') {
      return '';
    }

    return address
      .trim()
      .replace(/\s+/g, '')
      .replace(/[，。；]/g, ',')
      .replace(/,+/g, ',')
      .replace(/^,|,$/g, '')
      .replace(/上海市上海市/g, '上海市')
      .replace(/^上海/, '上海市');
  }

  /**
   * 解析地理编码结果
   * @param {Object} geocode - API返回的地理编码结果
   * @param {string} originalAddress - 原始地址
   * @returns {Object} 解析后的结果
   */
  parseGeocodeResult(geocode, originalAddress) {
    const location = geocode.location.split(',');
    const longitude = parseFloat(location[0]);
    const latitude = parseFloat(location[1]);

    return {
      originalAddress,
      formattedAddress: geocode.formatted_address,
      coordinates: {
        longitude,
        latitude
      },
      district: geocode.district || '',
      street: geocode.street || '',
      number: geocode.number || '',
      adcode: geocode.adcode || '',
      level: geocode.level || '',
      confidence: this.calculateConfidence(geocode.level),
      success: true
    };
  }

  /**
   * 计算地址匹配置信度
   * @param {string} level - 匹配级别
   * @returns {number} 置信度 (0-1)
   */
  calculateConfidence(level) {
    const confidenceMap = {
      '门址': 1.0,
      '门牌号': 0.9,
      '楼栋号': 0.8,
      '兴趣点': 0.7,
      '道路': 0.6,
      '道路交叉口': 0.5,
      '区县': 0.3,
      '城市': 0.1
    };

    return confidenceMap[level] || 0.5;
  }

  /**
   * API请求限流
   */
  async throttleRequest() {
    const now = Date.now();
    
    // 清理过期的请求记录
    this.requestQueue = this.requestQueue.filter(
      time => now - time < 1000
    );

    // 如果1秒内请求数超过限制，等待
    if (this.requestQueue.length >= this.maxRequestsPerSecond) {
      const waitTime = 1000 - (now - this.requestQueue[0]);
      if (waitTime > 0) {
        await this.sleep(waitTime);
      }
    }

    this.requestQueue.push(now);
  }

  /**
   * 休眠函数
   * @param {number} ms - 休眠毫秒数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 保存地理编码结果
   * @param {Array} results - 编码结果
   * @param {string} outputPath - 输出路径
   */
  async saveResults(results, outputPath) {
    try {
      await fs.ensureDir(path.dirname(outputPath));
      await fs.writeJson(outputPath, results, { spaces: 2 });
      console.log(`💾 地理编码结果已保存到: ${outputPath}`);
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
      throw error;
    }
  }

  /**
   * 加载缓存文件
   * @param {string} cacheFile - 缓存文件路径
   */
  async loadCache(cacheFile) {
    try {
      if (await fs.pathExists(cacheFile)) {
        const cacheData = await fs.readJson(cacheFile);
        this.cache = new Map(Object.entries(cacheData));
        console.log(`📂 已加载 ${this.cache.size} 个地址缓存`);
      }
    } catch (error) {
      console.warn('⚠️ 加载缓存失败:', error.message);
    }
  }

  /**
   * 保存缓存到文件
   * @param {string} cacheFile - 缓存文件路径
   */
  async saveCache(cacheFile) {
    try {
      const cacheData = Object.fromEntries(this.cache);
      await fs.writeJson(cacheFile, cacheData, { spaces: 2 });
      console.log(`💾 已保存 ${this.cache.size} 个地址缓存`);
    } catch (error) {
      console.error('❌ 保存缓存失败:', error.message);
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    return {
      totalRequests: this.requestCount,
      cacheSize: this.cache.size,
      cacheHitRate: this.cache.size > 0 ? (this.cache.size / (this.cache.size + this.requestCount)) : 0
    };
  }
}

export default Geocoder;