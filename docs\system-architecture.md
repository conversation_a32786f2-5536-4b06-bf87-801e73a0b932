# 系统架构设计文档

## 📋 项目概述

本系统是一个基于Web的放射性元素分布可视化平台，主要用于展示上海市范围内放射性物质的地理分布情况，提供交互式地图查看、详细信息展示、数据统计分析等功能。

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (Frontend)                     │
├─────────────────────────────────────────────────────────────┤
│  • React 18 + TypeScript                                   │
│  • Ant Design + Tailwind CSS                               │
│  • 高德地图API集成                                           │
│  • 响应式设计                                               │
└─────────────────────────────────────────────────────────────┘
                              │ HTTPS/REST API
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    API服务层 (Backend)                      │
├─────────────────────────────────────────────────────────────┤
│  • Node.js + Express                                       │
│  • RESTful API设计                                          │
│  • JWT身份认证                                              │
│  • 数据验证与错误处理                                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    业务逻辑层 (Services)                     │
├─────────────────────────────────────────────────────────────┤
│  • 数据查询服务                                             │
│  • 地理编码服务                                             │
│  • 统计分析服务                                             │
│  • 文件处理服务                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据处理层 (Data Processing)              │
├─────────────────────────────────────────────────────────────┤
│  • Excel文件解析                                            │
│  • 数据清洗与验证                                           │
│  • 地址地理编码                                             │
│  • 数据关联整合                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层 (Data Storage)                 │
├─────────────────────────────────────────────────────────────┤
│  • MongoDB - 主数据存储                                      │
│  • Redis - 缓存与会话                                       │
│  • 文件存储 - Excel上传文件                                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    外部服务层 (External Services)            │
├─────────────────────────────────────────────────────────────┤
│  • 高德地图API - 地理编码与地图服务                           │
│  • 监控告警服务                                             │
│  • 日志收集服务                                             │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心功能模块

### 1. 前端展示模块
- **地图可视化组件**: 基于高德地图API的交互式地图
- **数据展示组件**: 放射源详细信息展示
- **筛选搜索组件**: 多维度数据筛选功能
- **统计图表组件**: 数据统计和趋势分析
- **响应式布局**: 适配PC、平板、手机多端

### 2. 后端API模块
- **数据查询API**: 支持复杂条件查询和分页
- **地理服务API**: 地址解析和坐标转换
- **文件上传API**: Excel文件上传和处理
- **统计分析API**: 多维度数据统计
- **系统管理API**: 用户管理和系统配置

### 3. 数据处理模块
- **Excel解析器**: 支持多种Excel格式解析
- **数据清洗器**: 数据去重、格式化、验证
- **地理编码器**: 地址批量转换为坐标
- **数据整合器**: 多表关联和数据合并

### 4. 缓存策略模块
- **Redis缓存**: 热点数据缓存
- **地理编码缓存**: 避免重复API调用
- **查询结果缓存**: 提升响应速度
- **会话管理**: 用户状态保持

## 📊 数据模型设计

### 核心实体关系图

```
┌─────────────────────┐    ┌─────────────────────┐
│    RadiationSource  │    │  SourceDetail       │
│                     │    │                     │
│ + id                │───→│ + nuclideName       │
│ + unitName          │    │ + sourceCategory    │
│ + address           │    │ + sourceUsage       │
│ + coordinates       │    │ + sourceStatus      │
│ + legalPerson       │    │ + activity          │
│ + legalPersonPhone  │    │ + manufactureDate   │
│ + district          │    └─────────────────────┘
│ + sources[]         │
│ + licenseInfo       │    ┌─────────────────────┐
│ + overallStatus     │    │   LicenseInfo       │
│ + riskLevel         │    │                     │
│ + totalSources      │←───│ + licenseNumber     │
│ + activeSources     │    │ + issueDate         │
│ + createTime        │    │ + validityPeriod    │
│ + updateTime        │    │ + issuingAuthority  │
└─────────────────────┘    └─────────────────────┘
```

### 数据字段说明

#### 主表：RadiationSource（放射源主信息）
- `id`: 唯一标识符
- `unitName`: 单位名称（关联字段）
- `address`: 单位地址
- `coordinates`: 地理坐标 {longitude, latitude}
- `legalPerson`: 法人姓名
- `legalPersonPhone`: 法人电话
- `district`: 所属区域
- `sources[]`: 放射源详情数组
- `licenseInfo`: 许可证信息
- `overallStatus`: 总体状态 (normal/warning/danger)
- `riskLevel`: 风险等级 (low/medium/high)

#### 子表：SourceDetail（放射源详情）
- `nuclideName`: 核素名称
- `sourceCategory`: 放射源类别
- `sourceUsage`: 放射源用途
- `sourceStatus`: 放射源状态
- `activity`: 活度
- `manufactureDate`: 制造日期

## 🚀 技术选型说明

### 前端技术栈
| 技术 | 选择原因 | 版本 |
|------|----------|------|
| React 18 | 成熟的UI框架，组件化开发 | ^18.2.0 |
| TypeScript | 类型安全，提高代码质量 | ^5.0.0 |
| Ant Design | 企业级UI组件库 | ^5.12.0 |
| 高德地图API | 国内主流地图服务，准确性高 | 1.4.15 |
| Vite | 快速构建工具 | ^5.0.0 |

### 后端技术栈
| 技术 | 选择原因 | 版本 |
|------|----------|------|
| Node.js | JavaScript全栈开发 | >=16.0.0 |
| Express | 轻量级Web框架 | ^4.18.0 |
| MongoDB | 文档型数据库，适合地理空间数据 | ^8.0.0 |
| Redis | 高性能缓存数据库 | ^4.6.0 |
| Mongoose | MongoDB对象建模工具 | ^8.0.0 |

### 数据处理技术栈
| 技术 | 选择原因 | 版本 |
|------|----------|------|
| xlsx | Excel文件处理库 | ^0.18.0 |
| axios | HTTP客户端 | ^1.6.0 |
| lodash-es | 工具函数库 | ^4.17.0 |

## 🔒 安全策略

### 1. 数据安全
- 输入数据验证和过滤
- SQL注入防护（虽然使用NoSQL）
- XSS攻击防护
- 敏感数据加密存储

### 2. 接口安全
- JWT令牌认证
- API访问频率限制
- CORS跨域控制
- HTTPS强制加密传输

### 3. 系统安全
- 容器化部署隔离
- 定期安全更新
- 日志监控告警
- 数据备份策略

## 📈 性能优化策略

### 1. 前端优化
- 组件懒加载
- 图片懒加载
- 地图瓦片缓存
- 虚拟列表渲染
- Bundle分割优化

### 2. 后端优化
- MongoDB索引优化
- Redis缓存策略
- API响应压缩
- 连接池管理
- 查询结果分页

### 3. 数据库优化
- 地理空间索引
- 复合索引策略
- 查询优化
- 连接池配置
- 读写分离

## 🔄 部署架构

### 生产环境部署图

```
┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │     CDN         │
│   (Nginx)       │    │   (Static)      │
└─────────────────┘    └─────────────────┘
         │                       │
         ▼                       │
┌─────────────────┐              │
│  Frontend       │              │
│  (React SPA)    │◀─────────────┘
└─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │   Monitoring    │
│   (Rate Limit)  │    │   (Prometheus)  │
└─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│  Backend API    │    │     Redis       │
│  (Node.js)      │───→│    (Cache)      │
└─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│    MongoDB      │    │   File Storage  │
│   (Primary)     │    │   (Uploads)     │
└─────────────────┘    └─────────────────┘
```

### 容器化部署

- **Docker容器化**: 统一运行环境
- **Docker Compose**: 本地开发编排
- **Kubernetes**: 生产环境编排（可选）
- **CI/CD流水线**: 自动化部署

## 📊 监控与运维

### 1. 应用监控
- 系统性能指标
- API响应时间
- 错误率统计
- 用户行为分析

### 2. 基础设施监控
- 服务器资源使用
- 数据库性能
- 网络状况
- 存储空间

### 3. 日志管理
- 结构化日志格式
- 集中化日志收集
- 日志分析和告警
- 审计日志记录

## 🔮 扩展性设计

### 1. 水平扩展
- 无状态API设计
- 负载均衡支持
- 数据库分片策略
- 缓存集群部署

### 2. 功能扩展
- 插件化架构设计
- 多地图服务支持
- 多数据源接入
- 自定义报表生成

### 3. 集成扩展
- 第三方系统集成
- 数据导入导出
- API开放平台
- 移动端支持

---

*此文档会根据项目进展持续更新和完善。*