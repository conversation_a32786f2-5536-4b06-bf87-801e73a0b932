import React, { useEffect, useRef, useState } from 'react';
import { Card, Spin, message, Tooltip, Badge, Tag } from 'antd';
import { 
  EnvironmentOutlined, 
  ExclamationCircleOutlined,
  PhoneOutlined,
  UserOutlined 
} from '@ant-design/icons';
import type { RadioactiveSource } from '../types/radiation';

// 声明高德地图全局变量
declare global {
  interface Window {
    AMap: any;
    AMapUI: any;
  }
}

interface RadiationMapProps {
  data: RadioactiveSource[];
  loading?: boolean;
  onMarkerClick?: (source: RadioactiveSource) => void;
}

const RadiationMap: React.FC<RadiationMapProps> = ({
  data,
  loading = false,
  onMarkerClick
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const mapInstance = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const [mapLoading, setMapLoading] = useState(true);

  // 放射源状态颜色映射
  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      '使用中': '#52c41a',
      '暂存': '#faad14', 
      '废源': '#ff4d4f',
      '丢失': '#f50',
      '正常': '#52c41a',
      '异常': '#ff4d4f'
    };
    return statusColors[status] || '#1890ff';
  };

  // 放射源类别图标映射
  const getCategoryIcon = (category: string) => {
    const categoryIcons: Record<string, string> = {
      '密封放射源': '🔒',
      '非密封放射源': '🌊',
      'X射线装置': '⚡',
      '加速器': '🚀',
      '其他': '📡'
    };
    return categoryIcons[category] || '☢️';
  };

  // 初始化地图
  useEffect(() => {
    if (!mapContainer.current) return;

    const initMap = async () => {
      try {
        // 确保高德地图API已加载
        if (!window.AMap) {
          await loadAMapScript();
        }

        // 创建地图实例
        const map = new window.AMap.Map(mapContainer.current, {
          zoom: 10,
          center: [121.473701, 31.230416], // 上海市中心
          mapStyle: 'amap://styles/light',
          viewMode: '3D',
          pitch: 0,
          features: ['bg', 'road', 'building', 'point']
        });

        // 添加地图控件
        map.addControl(new window.AMap.Scale());
        map.addControl(new window.AMap.ToolBar());
        
        // 添加上海市边界
        const districtSearch = new window.AMap.DistrictSearch({
          extensions: 'all',
          subdistrict: 1
        });

        districtSearch.search('上海市', (status: string, result: any) => {
          if (status === 'complete') {
            const bounds = result.districtList[0].boundaries;
            bounds.forEach((boundary: any) => {
              const polygon = new window.AMap.Polygon({
                path: boundary,
                strokeColor: '#1890ff',
                strokeWeight: 2,
                strokeOpacity: 0.8,
                fillColor: '#1890ff',
                fillOpacity: 0.1,
                strokeStyle: 'solid'
              });
              map.add(polygon);
            });
          }
        });

        mapInstance.current = map;
        setMapLoading(false);
        
      } catch (error) {
        console.error('地图初始化失败:', error);
        message.error('地图加载失败，请检查网络连接');
        setMapLoading(false);
      }
    };

    initMap();

    return () => {
      if (mapInstance.current) {
        mapInstance.current.destroy();
      }
    };
  }, []);

  // 加载高德地图脚本
  const loadAMapScript = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.AMap) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://webapi.amap.com/maps?v=1.4.15&key=YOUR_AMAP_KEY&plugin=AMap.Scale,AMap.ToolBar,AMap.DistrictSearch`;
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('高德地图脚本加载失败'));
      document.head.appendChild(script);
    });
  };

  // 清除所有标记
  const clearMarkers = () => {
    markersRef.current.forEach(marker => {
      if (mapInstance.current) {
        mapInstance.current.remove(marker);
      }
    });
    markersRef.current = [];
  };

  // 添加标记点
  useEffect(() => {
    if (!mapInstance.current || !data.length || mapLoading) return;

    clearMarkers();

    data.forEach((source) => {
      if (!source.coordinates?.longitude || !source.coordinates?.latitude) {
        return;
      }

      // 创建自定义标记内容
      const markerContent = createMarkerContent(source);
      
      const marker = new window.AMap.Marker({
        position: [source.coordinates.longitude, source.coordinates.latitude],
        content: markerContent,
        anchor: 'center',
        offset: new window.AMap.Pixel(0, -20)
      });

      // 添加点击事件
      marker.on('click', () => {
        onMarkerClick?.(source);
      });

      // 添加鼠标悬停事件
      const infoWindow = createInfoWindow(source);
      
      marker.on('mouseover', () => {
        infoWindow.open(mapInstance.current, marker.getPosition());
      });

      marker.on('mouseout', () => {
        infoWindow.close();
      });

      mapInstance.current.add(marker);
      markersRef.current.push(marker);
    });

    // 自适应显示所有标记
    if (markersRef.current.length > 0) {
      mapInstance.current.setFitView(markersRef.current);
    }
  }, [data, mapLoading, onMarkerClick]);

  // 创建标记内容
  const createMarkerContent = (source: RadioactiveSource) => {
    const hasHighRiskSource = source.sources.some(s => 
      ['使用中', '异常'].includes(s.sourceStatus)
    );
    
    const statusColor = hasHighRiskSource ? '#ff4d4f' : '#52c41a';
    const sourceCount = source.sources.length;

    return `
      <div class="custom-marker" style="
        position: relative;
        width: 32px;
        height: 32px;
        background: ${statusColor};
        border: 2px solid #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: white;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        cursor: pointer;
        transition: all 0.3s ease;
      ">
        ☢️
        ${sourceCount > 1 ? `
          <span style="
            position: absolute;
            top: -8px;
            right: -8px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #fff;
          ">${sourceCount}</span>
        ` : ''}
      </div>
    `;
  };

  // 创建信息窗口
  const createInfoWindow = (source: RadioactiveSource) => {
    const sourcesHtml = source.sources.map(s => `
      <div style="margin: 8px 0; padding: 8px; background: #f5f5f5; border-radius: 4px;">
        <div style="display: flex; align-items: center; margin-bottom: 4px;">
          <span style="margin-right: 8px;">${getCategoryIcon(s.sourceCategory)}</span>
          <strong>${s.nuclideName}</strong>
          <span style="
            margin-left: 8px;
            padding: 2px 6px;
            background: ${getStatusColor(s.sourceStatus)};
            color: white;
            border-radius: 2px;
            font-size: 10px;
          ">${s.sourceStatus}</span>
        </div>
        <div style="font-size: 12px; color: #666;">
          类别: ${s.sourceCategory} | 用途: ${s.sourceUsage}
        </div>
      </div>
    `).join('');

    const content = `
      <div style="padding: 12px; min-width: 280px; max-width: 400px;">
        <div style="
          display: flex; 
          align-items: center; 
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e8e8e8;
        ">
          <span style="margin-right: 8px; font-size: 16px;">🏢</span>
          <strong style="font-size: 14px; color: #1890ff;">${source.unitName}</strong>
        </div>
        
        <div style="margin-bottom: 8px; font-size: 12px;">
          <span style="margin-right: 8px;">📍</span>
          <span style="color: #666;">${source.address}</span>
        </div>
        
        <div style="margin-bottom: 8px; font-size: 12px;">
          <span style="margin-right: 8px;">👤</span>
          <span style="color: #666;">法人: ${source.legalPerson}</span>
        </div>
        
        <div style="margin-bottom: 12px; font-size: 12px;">
          <span style="margin-right: 8px;">📞</span>
          <span style="color: #666;">${source.legalPersonPhone}</span>
        </div>
        
        <div style="
          margin-bottom: 8px; 
          font-size: 12px; 
          font-weight: bold;
          color: #1890ff;
        ">
          放射源详情 (${source.sources.length}个):
        </div>
        
        <div style="max-height: 200px; overflow-y: auto;">
          ${sourcesHtml}
        </div>
      </div>
    `;

    return new window.AMap.InfoWindow({
      content,
      autoMove: true,
      closeWhenClickMap: true
    });
  };

  return (
    <Card 
      className="radiation-map-container" 
      bodyStyle={{ padding: 0, height: '100%' }}
      style={{ height: '100%' }}
    >
      <Spin spinning={loading || mapLoading} tip="加载地图数据中...">
        <div 
          ref={mapContainer} 
          className="map-container"
          style={{ 
            width: '100%', 
            height: '600px',
            borderRadius: '8px',
            overflow: 'hidden'
          }}
        />
      </Spin>
      
      {/* 图例 */}
      <div style={{
        position: 'absolute',
        top: '16px',
        right: '16px',
        background: 'rgba(255, 255, 255, 0.9)',
        padding: '12px',
        borderRadius: '6px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        fontSize: '12px',
        minWidth: '160px'
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>图例</div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
          <div style={{ 
            width: '12px', 
            height: '12px', 
            background: '#52c41a', 
            borderRadius: '50%', 
            marginRight: '6px' 
          }}></div>
          <span>正常状态</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
          <div style={{ 
            width: '12px', 
            height: '12px', 
            background: '#ff4d4f', 
            borderRadius: '50%', 
            marginRight: '6px' 
          }}></div>
          <span>异常/使用中</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: '6px' }}>☢️</span>
          <span>放射源位置</span>
        </div>
      </div>
    </Card>
  );
};

export default RadiationMap;