#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务模块 - 基于MongoDB的放射性元素布点数据API
提供地图数据接口，支持实时查询和筛选
"""

from flask import Flask, jsonify, request, send_file
from flask_cors import CORS
import json
import os
from mongodb_manager import RadiationDataManager
import logging
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量存储数据库管理器
db_manager = None

def init_database():
    """初始化数据库连接"""
    global db_manager
    try:
        db_manager = RadiationDataManager()
        logger.info("✅ MongoDB数据库连接初始化成功")
        return True
    except Exception as e:
        logger.error(f"❌ MongoDB数据库连接初始化失败: {e}")
        return False

@app.route('/api/health')
def health_check():
    """健康检查接口"""
    try:
        if not db_manager:
            return jsonify({
                'status': 'error',
                'message': '数据库连接未初始化'
            }), 500
        
        # 测试数据库连接
        stats = db_manager.get_statistics()
        return jsonify({
            'status': 'ok',
            'database': 'connected',
            'stats': stats,
            'timestamp': str(datetime.now())
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/units')
def get_all_units():
    """获取所有核技术利用单位数据"""
    try:
        if not db_manager:
            return jsonify({'error': '数据库连接未初始化'}), 500
        
        # 获取查询参数
        district = request.args.get('district')
        has_sources = request.args.get('has_sources')
        geocoded_only = request.args.get('geocoded_only', 'false').lower() == 'true'
        limit = request.args.get('limit', type=int)
        
        # 构建筛选条件
        filters = {}
        if district:
            filters['district'] = district
        if has_sources:
            filters['source_count'] = {'$gt': 0}
        if geocoded_only:
            filters['geocoded'] = True
        
        # 查询数据
        units = db_manager.get_units_with_filters(filters, limit)
        
        # 添加地理编码信息
        units_with_location = []
        for unit in units:
            geocode = db_manager.get_geocode_by_unit_id(unit['_id'])
            if geocode:
                unit.update({
                    'longitude': geocode['longitude'],
                    'latitude': geocode['latitude'],
                    'geocoded': True
                })
            else:
                unit['geocoded'] = False
            
            # 转换ObjectId为字符串
            unit['_id'] = str(unit['_id'])
            units_with_location.append(unit)
        
        return jsonify({
            'success': True,
            'count': len(units_with_location),
            'data': units_with_location
        })
        
    except Exception as e:
        logger.error(f"获取单位数据失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/units/<unit_id>')
def get_unit_detail(unit_id):
    """获取单位详细信息，包括放射源清单"""
    try:
        if not db_manager:
            return jsonify({'error': '数据库连接未初始化'}), 500
        
        # 获取单位基本信息
        unit = db_manager.get_unit_by_id(unit_id)
        if not unit:
            return jsonify({'error': '未找到指定单位'}), 404
        
        # 获取地理编码信息
        geocode = db_manager.get_geocode_by_unit_id(unit_id)
        if geocode:
            unit.update({
                'longitude': geocode['longitude'],
                'latitude': geocode['latitude']
            })
        
        # 获取放射源信息
        sources = db_manager.get_sources_by_unit_id(unit_id)
        unit['sources'] = sources
        
        # 转换ObjectId为字符串
        unit['_id'] = str(unit['_id'])
        if 'sources' in unit:
            for source in unit['sources']:
                source['_id'] = str(source['_id'])
        
        return jsonify({
            'success': True,
            'data': unit
        })
        
    except Exception as e:
        logger.error(f"获取单位详细信息失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sources')
def get_sources():
    """获取放射源数据"""
    try:
        if not db_manager:
            return jsonify({'error': '数据库连接未初始化'}), 500
        
        # 获取查询参数
        nuclide = request.args.get('nuclide')
        category = request.args.get('category')
        district = request.args.get('district')
        limit = request.args.get('limit', type=int)
        
        # 构建筛选条件
        filters = {}
        if nuclide:
            filters['nuclide_name'] = {'$regex': nuclide, '$options': 'i'}
        if category:
            filters['source_category'] = {'$regex': category, '$options': 'i'}
        
        # 获取放射源数据
        sources = db_manager.get_sources_with_filters(filters, limit)
        
        # 如果需要按区域筛选，需要关联单位信息
        if district:
            filtered_sources = []
            for source in sources:
                unit = db_manager.get_unit_by_id(source['unit_id'])
                if unit and unit.get('district') == district:
                    source['unit_info'] = {
                        'name': unit.get('name'),
                        'address': unit.get('address'),
                        'district': unit.get('district')
                    }
                    filtered_sources.append(source)
            sources = filtered_sources
        
        # 转换ObjectId为字符串
        for source in sources:
            source['_id'] = str(source['_id'])
            source['unit_id'] = str(source['unit_id'])
        
        return jsonify({
            'success': True,
            'count': len(sources),
            'data': sources
        })
        
    except Exception as e:
        logger.error(f"获取放射源数据失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/statistics')
def get_statistics():
    """获取统计信息"""
    try:
        if not db_manager:
            return jsonify({'error': '数据库连接未初始化'}), 500
        
        stats = db_manager.get_statistics()
        
        # 获取更详细的统计信息
        detailed_stats = {
            'overview': {
                'total_units': stats['total_units'],
                'total_sources': stats['total_sources'],
                'geocoded_units': stats['geocoded_units'],
                'geocoding_rate': round(stats['geocoded_units'] / stats['total_units'] * 100, 1) if stats['total_units'] > 0 else 0
            },
            'districts': stats.get('districts', {}),
            'nuclides': stats.get('nuclides', {}),
            'source_categories': stats.get('source_categories', {})
        }
        
        return jsonify({
            'success': True,
            'data': detailed_stats
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/search')
def search_units():
    """搜索单位"""
    try:
        if not db_manager:
            return jsonify({'error': '数据库连接未初始化'}), 500
        
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'error': '搜索关键词不能为空'}), 400
        
        limit = request.args.get('limit', 50, type=int)
        
        # 执行搜索
        results = db_manager.search_units(query, limit)
        
        # 添加地理编码信息
        units_with_location = []
        for unit in results:
            geocode = db_manager.get_geocode_by_unit_id(unit['_id'])
            if geocode:
                unit.update({
                    'longitude': geocode['longitude'],
                    'latitude': geocode['latitude'],
                    'geocoded': True
                })
            else:
                unit['geocoded'] = False
            
            # 转换ObjectId为字符串
            unit['_id'] = str(unit['_id'])
            units_with_location.append(unit)
        
        return jsonify({
            'success': True,
            'query': query,
            'count': len(units_with_location),
            'data': units_with_location
        })
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/nearby')
def get_nearby_units():
    """获取附近的单位"""
    try:
        if not db_manager:
            return jsonify({'error': '数据库连接未初始化'}), 500
        
        # 获取参数
        lat = request.args.get('lat', type=float)
        lng = request.args.get('lng', type=float)
        radius = request.args.get('radius', 1000, type=int)  # 默认1公里
        limit = request.args.get('limit', 20, type=int)
        
        if not lat or not lng:
            return jsonify({'error': '缺少经纬度参数'}), 400
        
        # 获取附近单位
        nearby_units = db_manager.get_nearby_units(lng, lat, radius, limit)
        
        # 转换ObjectId为字符串
        for unit in nearby_units:
            unit['_id'] = str(unit['_id'])
        
        return jsonify({
            'success': True,
            'center': {'latitude': lat, 'longitude': lng},
            'radius': radius,
            'count': len(nearby_units),
            'data': nearby_units
        })
        
    except Exception as e:
        logger.error(f"获取附近单位失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/map-data')
def get_map_data():
    """获取地图数据（兼容旧版本接口）"""
    try:
        if not db_manager:
            return jsonify({'error': '数据库连接未初始化'}), 500
        
        # 获取所有有地理编码的单位
        units = db_manager.get_all_units_with_location()
        
        # 转换为旧版本格式
        map_data = []
        for unit in units:
            map_unit = {
                'id': str(unit['_id']),
                'name': unit.get('name', ''),
                'address': unit.get('address', ''),
                'district': unit.get('district', ''),
                'longitude': unit.get('longitude'),
                'latitude': unit.get('latitude'),
                'source_count': unit.get('source_count', 0),
                'legal_person': unit.get('legal_person', ''),
                'legal_phone': unit.get('legal_phone', ''),
                'geocoded': True
            }
            
            # 添加放射源统计信息
            sources = db_manager.get_sources_by_unit_id(unit['_id'])
            if sources:
                nuclides = list(set([s.get('nuclide_name', '') for s in sources if s.get('nuclide_name')]))
                categories = list(set([s.get('source_category', '') for s in sources if s.get('source_category')]))
                map_unit.update({
                    'main_nuclides': nuclides[:3],  # 取前3个
                    'categories': categories[:3]    # 取前3个
                })
            
            map_data.append(map_unit)
        
        return jsonify(map_data)
        
    except Exception as e:
        logger.error(f"获取地图数据失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/')
def index():
    """主页 - 重定向到地图页面"""
    return send_file('map_demo.html')

@app.route('/map')
def map_page():
    """地图页面"""
    return send_file('map_demo.html')

@app.route('/radiation_map_data_lite.json')
def get_lite_data():
    """提供轻量版地图数据文件"""
    try:
        return send_file('radiation_map_data_lite.json', mimetype='application/json')
    except FileNotFoundError:
        logger.error("❌ radiation_map_data_lite.json 文件不存在")
        return jsonify({'error': '数据文件不存在'}), 404

@app.route('/static/<path:filename>')
def serve_static(filename):
    """提供静态文件服务"""
    try:
        return send_file(filename)
    except FileNotFoundError:
        return jsonify({'error': f'文件 {filename} 不存在'}), 404

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({'error': '页面未找到'}), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

def create_app():
    """应用工厂函数"""
    # 初始化数据库
    if not init_database():
        logger.error("❌ 数据库初始化失败，请检查MongoDB连接")
        return None
    
    return app

if __name__ == '__main__':
    from datetime import datetime
    
    print("🚀 启动基于MongoDB的放射性元素布点可视化系统")
    print("=" * 60)
    
    # 创建应用
    app = create_app()
    if not app:
        print("❌ 应用启动失败")
        exit(1)
    
    # 显示可用的API端点
    print("\n📋 可用的API端点:")
    print("   GET  /api/health          - 健康检查")
    print("   GET  /api/units           - 获取所有单位")
    print("   GET  /api/units/<id>      - 获取单位详情")
    print("   GET  /api/sources         - 获取放射源数据")
    print("   GET  /api/statistics      - 获取统计信息")
    print("   GET  /api/search?q=关键词  - 搜索单位")
    print("   GET  /api/nearby          - 获取附近单位")
    print("   GET  /api/map-data        - 获取地图数据（兼容）")
    print("   GET  /                    - 地图页面")
    
    try:
        print(f"\n✅ 服务器已启动，访问地址:")
        print(f"   http://localhost:8000")
        print(f"   http://127.0.0.1:8000")
        print(f"\n⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n按 Ctrl+C 停止服务器")
        
        # 启动服务器
        app.run(host='0.0.0.0', port=8000, debug=False)
        
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
    finally:
        # 关闭数据库连接
        if db_manager:
            db_manager.close_connection()
            print("📊 数据库连接已关闭") 