import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import Redis from 'redis';

// 导入路由
import radiationRoutes from './routes/radiation.js';
import uploadRoutes from './routes/upload.js';
import statsRoutes from './routes/statistics.js';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// 中间件配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://webapi.amap.com"],
      scriptSrc: ["'self'", "https://webapi.amap.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://restapi.amap.com"]
    }
  }
}));

app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// MongoDB连接
const connectMongoDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/radiation_map', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB连接成功');
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    process.exit(1);
  }
};

// Redis连接
const connectRedis = async () => {
  try {
    const redis = Redis.createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    redis.on('error', (err) => {
      console.error('❌ Redis连接错误:', err);
    });
    
    await redis.connect();
    console.log('✅ Redis连接成功');
    
    // 将redis实例添加到app中供其他模块使用
    app.locals.redis = redis;
  } catch (error) {
    console.error('⚠️ Redis连接失败，将使用内存缓存:', error);
  }
};

// API路由
app.use('/api/radiation', radiationRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/statistics', statsRoutes);

// 健康检查接口
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    redis: app.locals.redis ? 'connected' : 'disconnected'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  
  res.status(error.status || 500).json({
    success: false,
    message: error.message || '服务器内部错误',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// 启动服务器
const startServer = async () => {
  try {
    await connectMongoDB();
    await connectRedis();
    
    app.listen(PORT, () => {
      console.log(`🚀 服务器启动成功，端口: ${PORT}`);
      console.log(`📡 API地址: http://localhost:${PORT}/api`);
      console.log(`🏥 健康检查: http://localhost:${PORT}/api/health`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('📴 收到SIGTERM信号，正在关闭服务器...');
  
  try {
    if (app.locals.redis) {
      await app.locals.redis.quit();
    }
    await mongoose.connection.close();
    console.log('✅ 服务器已优雅关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭服务器时发生错误:', error);
    process.exit(1);
  }
});

process.on('SIGINT', async () => {
  console.log('📴 收到SIGINT信号，正在关闭服务器...');
  
  try {
    if (app.locals.redis) {
      await app.locals.redis.quit();
    }
    await mongoose.connection.close();
    console.log('✅ 服务器已优雅关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭服务器时发生错误:', error);
    process.exit(1);
  }
});

// 启动应用
startServer();

export default app;