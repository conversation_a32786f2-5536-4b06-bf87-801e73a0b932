#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据整合脚本
将三个Excel文件的数据整合为适合地图可视化的JSON格式
"""

import pandas as pd
import json
import os
from datetime import datetime
import numpy as np

def load_excel_files():
    """加载三个Excel文件"""
    base_dir = r"C:\Users\<USER>\Desktop\radiation-map-system"
    
    # 加载文件
    basic_info = pd.read_excel(os.path.join(base_dir, "20250304核技术利用单位基本信息列表to消防.xls"))
    license_info = pd.read_excel(os.path.join(base_dir, "20250314持证单位列表.xls"))
    source_detail = pd.read_excel(os.path.join(base_dir, "20250314放射源明细.xls"))
    
    print(f"基本信息表: {len(basic_info)} 行")
    print(f"持证单位表: {len(license_info)} 行") 
    print(f"放射源明细表: {len(source_detail)} 行")
    
    return basic_info, license_info, source_detail

def merge_unit_data(basic_info, license_info):
    """合并单位基本信息和持证信息"""
    
    # 使用单位名称和许可证号作为关键字进行合并
    # 优先使用持证单位列表的数据（更新、更完整）
    merged_units = license_info.copy()
    
    # 从基本信息表补充缺失的数据
    for idx, row in basic_info.iterrows():
        unit_name = row['单位名称']
        license_no = row['许可证号']
        
        # 在持证单位表中查找对应记录
        matching_rows = merged_units[
            (merged_units['单位名称'] == unit_name) | 
            (merged_units['许可证号'] == license_no)
        ]
        
        if len(matching_rows) == 0:
            # 如果持证单位表中没有，添加到合并表中
            new_row = row.copy()
            # 统一字段名称
            if '所属区县' in new_row:
                new_row['所属地市'] = new_row['所属区县']
            merged_units = pd.concat([merged_units, new_row.to_frame().T], ignore_index=True)
    
    print(f"合并后单位数量: {len(merged_units)}")
    return merged_units

def process_addresses(merged_units):
    """处理地址信息，准备地理编码"""
    
    # 清理地址数据
    merged_units['cleaned_address'] = merged_units['单位地址'].fillna('')
    
    # 统一上海市地址格式
    def format_shanghai_address(address):
        if pd.isna(address) or address == '':
            return ''
        
        address = str(address).strip()
        
        # 如果地址不以"上海市"开头，添加它
        if not address.startswith('上海市'):
            # 检查是否包含区县信息
            districts = ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区',
                        '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区',
                        '奉贤区', '崇明区']
            
            found_district = None
            for district in districts:
                if district in address:
                    found_district = district
                    break
            
            if found_district:
                address = f"上海市{address}"
            else:
                # 如果没有明确的区县，根据所属地市添加
                if '所属地市' in merged_units.iloc[0] and pd.notna(merged_units.iloc[0]['所属地市']):
                    district = merged_units.iloc[0]['所属地市']
                    if district in districts:
                        address = f"上海市{district}{address}"
                    else:
                        address = f"上海市{address}"
                else:
                    address = f"上海市{address}"
        
        return address
    
    merged_units['formatted_address'] = merged_units['单位地址'].apply(format_shanghai_address)
    
    return merged_units

def create_radiation_map_data(merged_units, source_detail):
    """创建地图可视化所需的数据结构"""
    
    map_data = []
    
    for idx, unit in merged_units.iterrows():
        # 获取该单位的放射源详情
        unit_sources = source_detail[
            (source_detail['所属单位'] == unit['单位名称']) |
            (source_detail['许可证号'] == unit['许可证号'])
        ]
        
        # 构建单位基本信息
        unit_info = {
            'id': f"unit_{idx}",
            'name': unit['单位名称'],
            'license_no': unit['许可证号'],
            'address': unit['formatted_address'],
            'district': unit.get('所属地市', ''),
            'legal_person': unit.get('法人', ''),
            'legal_phone': unit.get('法人电话', ''),
            'approval_agency': unit.get('审批机关', ''),
            'license_date': unit.get('当前发证日期', ''),
            'expire_date': unit.get('有效期至', ''),
            'social_credit_code': unit.get('统一社会信用代码', ''),
            
            # 活动范围
            'radiation_source_scope': unit.get('放射源活动种类和范围', ''),
            'unsealed_material_scope': unit.get('非密封放射性物质活动种类和范围', ''),
            'radiation_device_scope': unit.get('射线装置活动种类和范围', ''),
            
            # 放射源详情
            'sources': [],
            'source_count': len(unit_sources),
            
            # 坐标信息（待地理编码）
            'longitude': None,
            'latitude': None,
            'geocoded': False
        }
        
        # 添加放射源详情
        for _, source in unit_sources.iterrows():
            source_info = {
                'source_code': source.get('放射源编码', ''),
                'nuclide_name': source.get('核素名称', ''),
                'country': source.get('国家（地区）', ''),
                'manufacturer': source.get('生产厂家', ''),
                'source_category': source.get('放射源类别', ''),
                'source_purpose': source.get('放射源用途', ''),
                'factory_activity': source.get('出厂活度（Bq）', 0),
                'current_activity': source.get('实时活度（Bq）', 0),
                'factory_date': source.get('出厂日期', ''),
                'label': source.get('标号', ''),
                'region': source.get('所属区域', ''),
                'status': source.get('放射源状态', ''),
                'current_province': source.get('源现使用省', '')
            }
            unit_info['sources'].append(source_info)
        
        map_data.append(unit_info)
    
    return map_data

def save_data(map_data):
    """保存处理后的数据"""
    
    # 保存为JSON格式
    output_file = "radiation_map_data.json"
    
    # 处理numpy类型的序列化问题
    def convert_numpy_types(obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        return obj
    
    # 转换数据类型
    for unit in map_data:
        for key, value in unit.items():
            if key == 'sources':
                for source in value:
                    for source_key, source_value in source.items():
                        source[source_key] = convert_numpy_types(source_value)
            else:
                unit[key] = convert_numpy_types(value)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(map_data, f, ensure_ascii=False, indent=2)
    
    print(f"数据已保存到: {output_file}")
    
    # 创建数据统计报告
    create_data_report(map_data)

def create_data_report(map_data):
    """创建数据统计报告"""
    
    total_units = len(map_data)
    total_sources = sum(unit['source_count'] for unit in map_data)
    
    # 按区县统计
    district_stats = {}
    nuclide_stats = {}
    source_category_stats = {}
    
    for unit in map_data:
        district = unit['district']
        if district:
            district_stats[district] = district_stats.get(district, 0) + 1
        
        for source in unit['sources']:
            nuclide = source['nuclide_name']
            if nuclide:
                nuclide_stats[nuclide] = nuclide_stats.get(nuclide, 0) + 1
            
            category = source['source_category']
            if category:
                source_category_stats[category] = source_category_stats.get(category, 0) + 1
    
    report = f"""
# 放射性元素布点数据统计报告

## 基本统计
- 总单位数量: {total_units}
- 总放射源数量: {total_sources}
- 平均每单位放射源数量: {total_sources/total_units:.2f}

## 按区县分布
"""
    
    for district, count in sorted(district_stats.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total_units * 100
        report += f"- {district}: {count} 个单位 ({percentage:.1f}%)\n"
    
    report += "\n## 按核素类型分布\n"
    for nuclide, count in sorted(nuclide_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
        percentage = count / total_sources * 100
        report += f"- {nuclide}: {count} 个放射源 ({percentage:.1f}%)\n"
    
    report += "\n## 按放射源类别分布\n"
    for category, count in sorted(source_category_stats.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total_sources * 100
        report += f"- {category}: {count} 个放射源 ({percentage:.1f}%)\n"
    
    # 保存报告
    with open("data_report.md", 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"统计报告已保存到: data_report.md")

def main():
    """主函数"""
    print("开始数据整合...")
    
    # 1. 加载Excel文件
    basic_info, license_info, source_detail = load_excel_files()
    
    # 2. 合并单位数据
    merged_units = merge_unit_data(basic_info, license_info)
    
    # 3. 处理地址信息
    merged_units = process_addresses(merged_units)
    
    # 4. 创建地图数据
    map_data = create_radiation_map_data(merged_units, source_detail)
    
    # 5. 保存数据
    save_data(map_data)
    
    print("数据整合完成！")
    
    # 显示一些样例数据
    print("\n样例单位信息:")
    for i, unit in enumerate(map_data[:3]):
        print(f"\n单位 {i+1}:")
        print(f"  名称: {unit['name']}")
        print(f"  地址: {unit['address']}")
        print(f"  所属区县: {unit['district']}")
        print(f"  放射源数量: {unit['source_count']}")
        if unit['sources']:
            print(f"  主要放射源: {unit['sources'][0]['nuclide_name']} ({unit['sources'][0]['source_category']})")

if __name__ == "__main__":
    main()