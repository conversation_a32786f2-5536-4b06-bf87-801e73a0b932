import mongoose from 'mongoose';

// 放射源详细信息架构
const radiationSourceDetailSchema = new mongoose.Schema({
  nuclideName: {
    type: String,
    required: true,
    trim: true
  },
  sourceCategory: {
    type: String,
    required: true,
    enum: ['密封放射源', '非密封放射源', 'X射线装置', '加速器', '其他'],
    trim: true
  },
  sourceUsage: {
    type: String,
    required: true,
    trim: true
  },
  sourceStatus: {
    type: String,
    required: true,
    enum: ['使用中', '暂存', '废源', '丢失', '正常', '异常', '已回收'],
    trim: true
  },
  activity: {
    type: String,
    trim: true
  },
  activityUnit: {
    type: String,
    trim: true
  },
  manufactureDate: {
    type: Date
  },
  serialNumber: {
    type: String,
    trim: true
  },
  manufacturer: {
    type: String,
    trim: true
  }
}, { _id: false });

// 许可证信息架构
const licenseInfoSchema = new mongoose.Schema({
  licenseNumber: {
    type: String,
    required: true,
    trim: true
  },
  issueDate: {
    type: Date,
    required: true
  },
  validityPeriod: {
    type: Date,
    required: true
  },
  issuingAuthority: {
    type: String,
    trim: true
  },
  scope: {
    type: String,
    trim: true
  }
}, { _id: false });

// 地理坐标架构
const coordinatesSchema = new mongoose.Schema({
  longitude: {
    type: Number,
    required: true,
    min: -180,
    max: 180
  },
  latitude: {
    type: Number,
    required: true,
    min: -90,
    max: 90
  }
}, { _id: false });

// 主要放射源信息架构
const radiationSourceSchema = new mongoose.Schema({
  // 基本信息
  unitName: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  address: {
    type: String,
    required: true,
    trim: true
  },
  coordinates: {
    type: coordinatesSchema,
    index: '2dsphere' // 地理空间索引
  },
  
  // 联系信息
  legalPerson: {
    type: String,
    required: true,
    trim: true
  },
  legalPersonPhone: {
    type: String,
    required: true,
    trim: true
  },
  contactPerson: {
    type: String,
    trim: true
  },
  contactPhone: {
    type: String,
    trim: true
  },
  
  // 放射源详情数组
  sources: {
    type: [radiationSourceDetailSchema],
    required: true,
    validate: {
      validator: function(sources) {
        return sources && sources.length > 0;
      },
      message: '至少需要一个放射源记录'
    }
  },
  
  // 许可证信息
  licenseInfo: licenseInfoSchema,
  
  // 行政区划信息
  district: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  streetOffice: {
    type: String,
    trim: true
  },
  community: {
    type: String,
    trim: true
  },
  
  // 状态信息
  overallStatus: {
    type: String,
    enum: ['normal', 'warning', 'danger'],
    default: 'normal',
    index: true
  },
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'low',
    index: true
  },
  
  // 统计信息
  totalSources: {
    type: Number,
    default: 0
  },
  activeSources: {
    type: Number,
    default: 0
  },
  inactiveSources: {
    type: Number,
    default: 0
  },
  
  // 时间信息
  lastInspectionTime: {
    type: Date
  }
}, {
  timestamps: true, // 自动添加 createdAt 和 updatedAt
  collection: 'radiationSources'
});

// 虚拟字段：计算总体状态
radiationSourceSchema.virtual('computedStatus').get(function() {
  const hasAbnormal = this.sources.some(source => 
    ['异常', '丢失'].includes(source.sourceStatus)
  );
  const hasUsing = this.sources.some(source => 
    source.sourceStatus === '使用中'
  );
  
  if (hasAbnormal) return 'danger';
  if (hasUsing) return 'warning';
  return 'normal';
});

// 虚拟字段：计算风险等级
radiationSourceSchema.virtual('computedRiskLevel').get(function() {
  const dangerousCount = this.sources.filter(source =>
    ['异常', '丢失', '使用中'].includes(source.sourceStatus)
  ).length;
  
  const riskRatio = dangerousCount / this.sources.length;
  
  if (riskRatio >= 0.5) return 'high';
  if (riskRatio >= 0.2) return 'medium';
  return 'low';
});

// 中间件：保存前自动计算统计信息
radiationSourceSchema.pre('save', function(next) {
  this.totalSources = this.sources.length;
  this.activeSources = this.sources.filter(source => 
    source.sourceStatus === '使用中'
  ).length;
  this.inactiveSources = this.totalSources - this.activeSources;
  
  // 更新状态和风险等级
  this.overallStatus = this.computedStatus;
  this.riskLevel = this.computedRiskLevel;
  
  next();
});

// 静态方法：按区域查询
radiationSourceSchema.statics.findByDistrict = function(district) {
  return this.find({ district: new RegExp(district, 'i') });
};

// 静态方法：按状态查询
radiationSourceSchema.statics.findByStatus = function(status) {
  return this.find({ overallStatus: status });
};

// 静态方法：按风险等级查询
radiationSourceSchema.statics.findByRiskLevel = function(riskLevel) {
  return this.find({ riskLevel });
};

// 静态方法：地理位置附近查询
radiationSourceSchema.statics.findNearby = function(longitude, latitude, maxDistance = 1000) {
  return this.find({
    coordinates: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance
      }
    }
  });
};

// 静态方法：复合搜索
radiationSourceSchema.statics.search = function(params) {
  const query = {};
  
  if (params.keyword) {
    query.$or = [
      { unitName: new RegExp(params.keyword, 'i') },
      { address: new RegExp(params.keyword, 'i') },
      { legalPerson: new RegExp(params.keyword, 'i') },
      { 'sources.nuclideName': new RegExp(params.keyword, 'i') }
    ];
  }
  
  if (params.district) {
    query.district = new RegExp(params.district, 'i');
  }
  
  if (params.overallStatus) {
    query.overallStatus = params.overallStatus;
  }
  
  if (params.riskLevel) {
    query.riskLevel = params.riskLevel;
  }
  
  if (params.sourceStatus) {
    query['sources.sourceStatus'] = params.sourceStatus;
  }
  
  if (params.sourceCategory) {
    query['sources.sourceCategory'] = params.sourceCategory;
  }
  
  return this.find(query);
};

// 索引定义
radiationSourceSchema.index({ unitName: 'text', address: 'text', legalPerson: 'text' });
radiationSourceSchema.index({ district: 1, overallStatus: 1 });
radiationSourceSchema.index({ riskLevel: 1, updatedAt: -1 });
radiationSourceSchema.index({ 'sources.sourceStatus': 1 });
radiationSourceSchema.index({ 'sources.sourceCategory': 1 });

const RadiationSource = mongoose.model('RadiationSource', radiationSourceSchema);

export default RadiationSource;