import XLSX from 'xlsx';
import fs from 'fs-extra';
import path from 'path';

/**
 * Excel文件解析器
 * 用于解析放射源相关的三张Excel表格
 */
class ExcelParser {
  constructor() {
    this.parsedData = {
      basicInfo: [],     // 核技术利用单位基本信息
      licensedUnits: [], // 持证单位列表
      sourceDetails: []  // 放射源明细
    };
  }

  /**
   * 解析Excel文件
   * @param {string} filePath - Excel文件路径
   * @param {string} type - 文件类型 ('basic', 'licensed', 'details')
   * @returns {Array} 解析后的数据数组
   */
  parseExcelFile(filePath, type) {
    try {
      console.log(`📄 开始解析文件: ${filePath}`);
      
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0]; // 使用第一个工作表
      const worksheet = workbook.Sheets[sheetName];
      
      // 转换为JSON格式
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1, // 使用数组格式，第一行作为标题
        defval: '' // 空单元格默认值
      });

      if (jsonData.length < 2) {
        throw new Error('Excel文件内容为空或格式不正确');
      }

      // 获取标题行和数据行
      const headers = jsonData[0];
      const dataRows = jsonData.slice(1);

      console.log(`📊 找到 ${headers.length} 列，${dataRows.length} 行数据`);

      // 根据文件类型解析数据
      const parsedData = this.parseDataByType(headers, dataRows, type);
      
      // 存储解析结果
      this.parsedData[this.getDataKey(type)] = parsedData;
      
      console.log(`✅ 文件解析完成: ${parsedData.length} 条记录`);
      return parsedData;

    } catch (error) {
      console.error(`❌ 解析文件失败: ${filePath}`, error.message);
      throw error;
    }
  }

  /**
   * 根据文件类型解析数据
   * @param {Array} headers - 表头数组
   * @param {Array} dataRows - 数据行数组
   * @param {string} type - 文件类型
   * @returns {Array} 解析后的数据
   */
  parseDataByType(headers, dataRows, type) {
    const fieldMappings = this.getFieldMappings(type);
    const parsedData = [];

    dataRows.forEach((row, index) => {
      try {
        const record = {};
        
        // 映射字段
        headers.forEach((header, colIndex) => {
          const normalizedHeader = this.normalizeHeader(header);
          const fieldMapping = fieldMappings[normalizedHeader];
          
          if (fieldMapping) {
            const value = row[colIndex];
            record[fieldMapping.field] = this.processValue(value, fieldMapping.type);
          }
        });

        // 添加记录ID和类型
        record.id = `${type}_${index + 1}`;
        record.sourceType = type;
        record.rowIndex = index + 1;

        parsedData.push(record);

      } catch (error) {
        console.warn(`⚠️ 第 ${index + 1} 行数据解析失败:`, error.message);
      }
    });

    return parsedData;
  }

  /**
   * 获取字段映射配置
   * @param {string} type - 文件类型
   * @returns {Object} 字段映射配置
   */
  getFieldMappings(type) {
    const mappings = {
      basic: {
        '单位名称': { field: 'unitName', type: 'string' },
        '单位地址': { field: 'address', type: 'string' },
        '法人': { field: 'legalPerson', type: 'string' },
        '法人电话': { field: 'legalPersonPhone', type: 'string' },
        '联系人': { field: 'contactPerson', type: 'string' },
        '联系电话': { field: 'contactPhone', type: 'string' },
        '所属区域': { field: 'district', type: 'string' },
        '行政区': { field: 'district', type: 'string' },
        '区县': { field: 'district', type: 'string' }
      },
      licensed: {
        '单位名称': { field: 'unitName', type: 'string' },
        '所属单位': { field: 'unitName', type: 'string' },
        '许可证号': { field: 'licenseNumber', type: 'string' },
        '发证日期': { field: 'issueDate', type: 'date' },
        '有效期': { field: 'validityPeriod', type: 'date' },
        '发证机关': { field: 'issuingAuthority', type: 'string' },
        '许可范围': { field: 'scope', type: 'string' }
      },
      details: {
        '单位名称': { field: 'unitName', type: 'string' },
        '所属单位': { field: 'unitName', type: 'string' },
        '核素名称': { field: 'nuclideName', type: 'string' },
        '放射源类别': { field: 'sourceCategory', type: 'string' },
        '放射源用途': { field: 'sourceUsage', type: 'string' },
        '放射源状态': { field: 'sourceStatus', type: 'string' },
        '活度': { field: 'activity', type: 'string' },
        '活度单位': { field: 'activityUnit', type: 'string' },
        '制造日期': { field: 'manufactureDate', type: 'date' },
        '序列号': { field: 'serialNumber', type: 'string' },
        '制造商': { field: 'manufacturer', type: 'string' }
      }
    };

    return mappings[type] || {};
  }

  /**
   * 标准化表头名称
   * @param {string} header - 原始表头
   * @returns {string} 标准化后的表头
   */
  normalizeHeader(header) {
    if (!header) return '';
    
    return header
      .toString()
      .trim()
      .replace(/\s+/g, '')
      .replace(/[（）()]/g, '')
      .replace(/：/g, '');
  }

  /**
   * 处理单元格值
   * @param {any} value - 原始值
   * @param {string} type - 数据类型
   * @returns {any} 处理后的值
   */
  processValue(value, type) {
    if (value === null || value === undefined || value === '') {
      return '';
    }

    switch (type) {
      case 'string':
        return String(value).trim();
      
      case 'number':
        const num = parseFloat(value);
        return isNaN(num) ? 0 : num;
      
      case 'date':
        if (value instanceof Date) {
          return value;
        }
        
        // 尝试解析Excel日期格式
        if (typeof value === 'number') {
          const date = XLSX.SSF.parse_date_code(value);
          return new Date(date.y, date.m - 1, date.d);
        }
        
        // 尝试解析字符串日期
        const parsedDate = new Date(value);
        return isNaN(parsedDate.getTime()) ? null : parsedDate;
      
      default:
        return value;
    }
  }

  /**
   * 获取数据键名
   * @param {string} type - 文件类型
   * @returns {string} 数据键名
   */
  getDataKey(type) {
    const keyMap = {
      basic: 'basicInfo',
      licensed: 'licensedUnits',
      details: 'sourceDetails'
    };
    return keyMap[type] || type;
  }

  /**
   * 解析所有文件
   * @param {Object} filePaths - 文件路径配置
   * @returns {Object} 所有解析后的数据
   */
  async parseAllFiles(filePaths) {
    try {
      console.log('🚀 开始批量解析Excel文件...');

      // 解析基本信息文件
      if (filePaths.basic) {
        await this.parseExcelFile(filePaths.basic, 'basic');
      }

      // 解析持证单位文件
      if (filePaths.licensed) {
        await this.parseExcelFile(filePaths.licensed, 'licensed');
      }

      // 解析放射源明细文件
      if (filePaths.details) {
        await this.parseExcelFile(filePaths.details, 'details');
      }

      console.log('✅ 所有文件解析完成');
      console.log(`📊 解析结果统计:`);
      console.log(`   - 基本信息: ${this.parsedData.basicInfo.length} 条`);
      console.log(`   - 持证单位: ${this.parsedData.licensedUnits.length} 条`);
      console.log(`   - 放射源明细: ${this.parsedData.sourceDetails.length} 条`);

      return this.parsedData;

    } catch (error) {
      console.error('❌ 批量解析失败:', error.message);
      throw error;
    }
  }

  /**
   * 导出解析结果为JSON文件
   * @param {string} outputDir - 输出目录
   */
  async exportToJson(outputDir = './output') {
    try {
      await fs.ensureDir(outputDir);

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const outputFile = path.join(outputDir, `parsed-data-${timestamp}.json`);

      await fs.writeJson(outputFile, this.parsedData, { spaces: 2 });
      
      console.log(`💾 解析结果已保存到: ${outputFile}`);
      return outputFile;

    } catch (error) {
      console.error('❌ 导出JSON失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取解析统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    return {
      basicInfo: {
        count: this.parsedData.basicInfo.length,
        uniqueUnits: new Set(this.parsedData.basicInfo.map(item => item.unitName)).size
      },
      licensedUnits: {
        count: this.parsedData.licensedUnits.length,
        uniqueUnits: new Set(this.parsedData.licensedUnits.map(item => item.unitName)).size
      },
      sourceDetails: {
        count: this.parsedData.sourceDetails.length,
        uniqueUnits: new Set(this.parsedData.sourceDetails.map(item => item.unitName)).size,
        uniqueNuclides: new Set(this.parsedData.sourceDetails.map(item => item.nuclideName)).size
      }
    };
  }
}

export default ExcelParser;