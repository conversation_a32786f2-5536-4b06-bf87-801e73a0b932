# 上海市放射性元素布点可视化系统

## 项目概述

这是一个基于Web的地图可视化系统，用于展示上海市行政区域内放射性元素的分布情况。系统整合了三个Excel数据源，提供交互式地图界面，支持鼠标悬停显示详细信息、数据筛选等功能。

## 📁 项目结构

```
radiation-map-system/
├── 数据文件/
│   ├── 20250304核技术利用单位基本信息列表to消防.xls    # 基本信息表
│   ├── 20250314持证单位列表.xls                        # 持证单位表  
│   └── 20250314放射源明细.xls                          # 放射源明细表
│
├── 数据处理脚本/
│   ├── analyze_excel.py              # Excel文件分析脚本
│   ├── fix_encoding.py               # 数据整合和编码修复脚本
│   └── geocoding.py                  # 地理编码脚本
│
├── 生成的数据文件/
│   ├── radiation_map_data_fixed.json      # 修复编码后的完整数据
│   ├── radiation_map_data_geocoded.json   # 地理编码后的完整数据
│   ├── radiation_map_data_lite.json       # 轻量版地图数据
│   └── data_report.md                     # 数据统计报告
│
├── 可视化系统/
│   ├── map_demo.html                 # 地图可视化页面
│   └── start_server.py               # 本地服务器启动脚本
│
├── 配置文档/
│   ├── API_CONFIG.md                 # 高德地图API配置说明
│   └── PROJECT_README.md             # 项目说明文档（本文件）
│
└── 项目架构/
    ├── backend/                      # 后端API服务（预留）
    ├── frontend/                     # 前端React应用（预留）
    ├── data-processor/               # 数据处理模块（预留）
    └── docs/                         # 项目文档（预留）
```

## 📊 数据统计

根据最新的数据分析报告：

- **总单位数量**: 4,589个
- **总放射源数量**: 6,084个  
- **平均每单位放射源数量**: 1.33个

### 按区县分布 (前5名)
1. 浦东新区: 883 个单位 (19.2%)
2. 闵行区: 460 个单位 (10.0%)
3. 嘉定区: 354 个单位 (7.7%)
4. 静安区: 305 个单位 (6.6%)
5. 松江区: 292 个单位 (6.4%)

### 按核素类型分布 (前5名)
1. Co-60: 2,999 个放射源 (49.3%)
2. Ni-63: 1,249 个放射源 (20.5%)
3. Cs-137: 926 个放射源 (15.2%)
4. Kr-85: 161 个放射源 (2.6%)
5. Am-241: 145 个放射源 (2.4%)

### 按放射源类别分布
- Ⅴ类 (极低危险源): 2,451 个 (40.3%)
- Ⅰ类 (极高危险源): 2,112 个 (34.7%)
- Ⅱ类 (高危险源): 845 个 (13.9%)
- Ⅳ类 (较低危险源): 611 个 (10.0%)
- Ⅲ类 (中等危险源): 65 个 (1.1%)

## 🚀 快速开始

### 1. 环境准备

确保系统已安装Python 3.7+，并安装必要依赖：

```bash
pip install pandas openpyxl xlrd requests
```

### 2. 数据处理

如果需要重新处理Excel数据：

```bash
# 1. 分析Excel文件结构
python analyze_excel.py

# 2. 整合数据并修复编码
python fix_encoding.py

# 3. 进行地理编码（可选配置高德API Key）
python geocoding.py
```

### 3. 启动可视化系统

```bash
# 启动本地服务器
python start_server.py
```

系统将自动打开浏览器并访问 `http://localhost:8000/map_demo.html`

## 🗺️ 系统功能

### 主要特性
- **交互式地图**: 基于高德地图API的可视化界面
- **数据筛选**: 支持按区县、放射源类别、单位名称筛选  
- **详细信息**: 鼠标点击标记点显示单位详细信息
- **统计面板**: 实时显示筛选后的统计数据
- **响应式设计**: 支持PC和移动设备访问

### 标记点颜色说明
- 🔴 红色: Ⅰ类 (极高危险源)
- 🟠 橙色: Ⅱ类 (高危险源) 
- 🟡 黄色: Ⅲ类 (中等危险源)
- 🔵 蓝色: Ⅳ类 (较低危险源)
- 🟢 绿色: Ⅴ类 (极低危险源)

### 信息窗口内容
点击地图上的标记点可查看：
- 单位名称和地址
- 法人姓名和联系电话
- 放射源数量和主要核素类型
- 放射源类别信息

## ⚙️ 配置说明

### 高德地图API配置

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册账号并创建应用
3. 获取Web服务API Key
4. 修改以下文件中的API Key：
   - `map_demo.html` 第7行
   - `geocoding.py` 第11行

详细配置请参考 `API_CONFIG.md` 文件。

### 演示模式

如未配置API Key，系统将使用演示坐标数据，基于各区域中心位置生成随机分布的坐标点。

## 📝 数据源说明

### 原始数据文件
1. **20250304核技术利用单位基本信息列表to消防.xls**: 包含单位基本信息、许可证信息、联系方式等
2. **20250314持证单位列表.xls**: 包含持证单位的详细信息和活动范围
3. **20250314放射源明细.xls**: 包含具体放射源的技术参数和状态信息

### 数据关联逻辑
- 主要通过 `单位名称` 和 `许可证号` 进行数据关联
- 放射源明细通过 `所属单位` 字段与单位信息关联
- 系统自动处理重复数据并优先使用最新的持证单位列表数据

## 🔧 技术架构

### 前端技术栈
- HTML5 + CSS3 + JavaScript ES6+
- 高德地图JavaScript API v2.0
- 响应式设计，支持移动端

### 数据处理
- Python 3.x
- pandas (Excel数据处理)
- requests (HTTP请求)
- JSON (数据存储格式)

### 部署方式
- 本地HTTP服务器 (开发/演示)
- 可扩展为Nginx + 静态文件部署
- 支持云服务器部署

## 🚀 未来规划

### Phase 1 - 增强功能
- [ ] 添加热力图显示模式
- [ ] 支持数据导出功能
- [ ] 添加时间轴显示历史变化
- [ ] 集成更多地图底图选项

### Phase 2 - 后端系统
- [ ] 构建Node.js + MongoDB后端API
- [ ] 实现数据实时更新机制
- [ ] 添加用户权限管理
- [ ] 支持数据在线编辑

### Phase 3 - 高级分析
- [ ] 放射源活度衰减计算
- [ ] 风险评估模型集成
- [ ] 智能预警系统
- [ ] 移动端App开发

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: GitHub Issues页面
- 技术支持: 请在Issue中详细描述问题
- 功能建议: 欢迎提交Feature Request

---

**版本**: v1.0.0  
**更新日期**: 2025年1月  
**兼容性**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+