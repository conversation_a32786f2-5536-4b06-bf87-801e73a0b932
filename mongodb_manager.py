#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB数据库管理模块
用于管理放射性元素布点可视化系统的数据存储和查询
"""

import pymongo
from pymongo import MongoClient
from bson import ObjectId
import json
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional, Any
import logging
from config import Config, get_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RadiationDataManager:
    """放射性数据管理器"""
    
    def __init__(self, config: Config = None):
        """
        初始化数据库连接
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or get_config()
        self.connection_string = self.config.get_mongodb_uri()
        self.client = None
        self.db = None
        self.collections = {
            'units': 'radiation_units',           # 核技术利用单位基本信息
            'sources': 'radiation_sources',       # 放射源详细信息
            'licenses': 'unit_licenses',          # 许可证信息
            'geocodes': 'unit_geocodes'          # 地理编码信息
        }
        
        self.connect()
        self.create_indexes()
    
    def connect(self):
        """连接MongoDB数据库"""
        try:
            self.client = MongoClient(self.connection_string)
            # 从连接字符串中提取数据库名
            db_name = self.connection_string.split('/')[-1]
            self.db = self.client[db_name]
            
            # 测试连接
            self.db.command('ping')
            logger.info(f"✅ MongoDB连接成功: {db_name}")
            
        except Exception as e:
            logger.error(f"❌ MongoDB连接失败: {e}")
            raise
    
    def create_indexes(self):
        """创建数据库索引以提高查询性能"""
        try:
            # 单位表索引
            self.db[self.collections['units']].create_index([("name", 1)])
            self.db[self.collections['units']].create_index([("license_no", 1)])
            self.db[self.collections['units']].create_index([("district", 1)])
            self.db[self.collections['units']].create_index([("social_credit_code", 1)])
            
            # 放射源表索引
            self.db[self.collections['sources']].create_index([("unit_id", 1)])
            self.db[self.collections['sources']].create_index([("nuclide_name", 1)])
            self.db[self.collections['sources']].create_index([("source_category", 1)])
            
            # 地理位置索引
            self.db[self.collections['geocodes']].create_index([("location", "2dsphere")])
            self.db[self.collections['geocodes']].create_index([("unit_id", 1)])
            
            logger.info("✅ 数据库索引创建成功")
            
        except Exception as e:
            logger.warning(f"⚠️ 索引创建警告: {e}")
    
    def insert_unit(self, unit_data: Dict) -> str:
        """
        插入单位信息
        
        Args:
            unit_data: 单位数据字典
            
        Returns:
            插入记录的ObjectId字符串
        """
        try:
            # 添加创建时间
            unit_data['created_at'] = datetime.now()
            unit_data['updated_at'] = datetime.now()
            
            result = self.db[self.collections['units']].insert_one(unit_data)
            logger.info(f"✅ 插入单位: {unit_data.get('name', 'Unknown')}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"❌ 插入单位失败: {e}")
            raise
    
    def insert_sources(self, sources_data: List[Dict], unit_id: str):
        """
        插入放射源信息
        
        Args:
            sources_data: 放射源数据列表
            unit_id: 关联的单位ID
        """
        try:
            for source in sources_data:
                source['unit_id'] = ObjectId(unit_id)
                source['created_at'] = datetime.now()
            
            if sources_data:
                self.db[self.collections['sources']].insert_many(sources_data)
                logger.info(f"✅ 插入放射源: {len(sources_data)} 个")
                
        except Exception as e:
            logger.error(f"❌ 插入放射源失败: {e}")
            raise
    
    def insert_geocode(self, unit_id: str, longitude: float, latitude: float, address: str):
        """
        插入地理编码信息
        
        Args:
            unit_id: 单位ID
            longitude: 经度
            latitude: 纬度 
            address: 地址
        """
        try:
            geocode_data = {
                'unit_id': ObjectId(unit_id),
                'location': {
                    'type': 'Point',
                    'coordinates': [longitude, latitude]
                },
                'address': address,
                'longitude': longitude,
                'latitude': latitude,
                'geocoded_at': datetime.now()
            }
            
            self.db[self.collections['geocodes']].insert_one(geocode_data)
            logger.info(f"✅ 插入地理编码: {address}")
            
        except Exception as e:
            logger.error(f"❌ 插入地理编码失败: {e}")
            raise
    
    def get_all_units_with_location(self) -> List[Dict]:
        """
        获取所有有地理位置信息的单位数据
        
        Returns:
            单位数据列表，包含地理位置信息
        """
        try:
            pipeline = [
                {
                    '$lookup': {
                        'from': self.collections['geocodes'],
                        'localField': '_id',
                        'foreignField': 'unit_id',
                        'as': 'location_info'
                    }
                },
                {
                    '$lookup': {
                        'from': self.collections['sources'],
                        'localField': '_id',
                        'foreignField': 'unit_id',
                        'as': 'sources'
                    }
                },
                {
                    '$match': {
                        'location_info': {'$ne': []}
                    }
                },
                {
                    '$addFields': {
                        'location': {'$arrayElemAt': ['$location_info', 0]},
                        'source_count': {'$size': '$sources'}
                    }
                }
            ]
            
            results = list(self.db[self.collections['units']].aggregate(pipeline))
            
            # 转换ObjectId为字符串
            for result in results:
                result['_id'] = str(result['_id'])
                if 'sources' in result:
                    for source in result['sources']:
                        source['_id'] = str(source['_id'])
                        source['unit_id'] = str(source['unit_id'])
            
            logger.info(f"✅ 查询到 {len(results)} 个有位置信息的单位")
            return results
            
        except Exception as e:
            logger.error(f"❌ 查询单位数据失败: {e}")
            raise
    
    def get_units_by_district(self, district: str) -> List[Dict]:
        """
        按区域查询单位
        
        Args:
            district: 区域名称
            
        Returns:
            该区域的单位列表
        """
        try:
            units = list(self.db[self.collections['units']].find({'district': district}))
            for unit in units:
                unit['_id'] = str(unit['_id'])
            
            logger.info(f"✅ 查询到 {district} 的 {len(units)} 个单位")
            return units
            
        except Exception as e:
            logger.error(f"❌ 按区域查询失败: {e}")
            raise
    
    def get_units_near_location(self, longitude: float, latitude: float, radius_km: float = 5.0) -> List[Dict]:
        """
        查询指定位置附近的单位
        
        Args:
            longitude: 经度
            latitude: 纬度
            radius_km: 搜索半径（公里）
            
        Returns:
            附近的单位列表
        """
        try:
            # 先查询附近的地理编码记录
            nearby_geocodes = self.db[self.collections['geocodes']].find({
                'location': {
                    '$near': {
                        '$geometry': {
                            'type': 'Point',
                            'coordinates': [longitude, latitude]
                        },
                        '$maxDistance': radius_km * 1000  # 转换为米
                    }
                }
            })
            
            # 获取单位ID列表
            unit_ids = [geocode['unit_id'] for geocode in nearby_geocodes]
            
            # 查询对应的单位信息
            units = list(self.db[self.collections['units']].find({
                '_id': {'$in': unit_ids}
            }))
            
            for unit in units:
                unit['_id'] = str(unit['_id'])
            
            logger.info(f"✅ 查询到附近 {len(units)} 个单位")
            return units
            
        except Exception as e:
            logger.error(f"❌ 附近位置查询失败: {e}")
            raise
    
    def update_unit_geocode(self, unit_id: str, longitude: float, latitude: float):
        """
        更新单位的地理编码信息
        
        Args:
            unit_id: 单位ID
            longitude: 经度
            latitude: 纬度
        """
        try:
            # 更新或插入地理编码信息
            self.db[self.collections['geocodes']].update_one(
                {'unit_id': ObjectId(unit_id)},
                {
                    '$set': {
                        'location': {
                            'type': 'Point',
                            'coordinates': [longitude, latitude]
                        },
                        'longitude': longitude,
                        'latitude': latitude,
                        'geocoded_at': datetime.now()
                    }
                },
                upsert=True
            )
            
            logger.info(f"✅ 更新地理编码: {unit_id}")
            
        except Exception as e:
            logger.error(f"❌ 更新地理编码失败: {e}")
            raise
    
    def get_statistics(self) -> Dict:
        """
        获取数据统计信息
        
        Returns:
            统计信息字典
        """
        try:
            stats = {
                'total_units': self.db[self.collections['units']].count_documents({}),
                'total_sources': self.db[self.collections['sources']].count_documents({}),
                'geocoded_units': self.db[self.collections['geocodes']].count_documents({}),
                'districts': {},
                'source_categories': {},
                'nuclides': {}
            }
            
            # 按区域统计
            district_pipeline = [
                {'$group': {'_id': '$district', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}}
            ]
            districts = list(self.db[self.collections['units']].aggregate(district_pipeline))
            stats['districts'] = {item['_id']: item['count'] for item in districts}
            
            # 按放射源类别统计
            category_pipeline = [
                {'$group': {'_id': '$source_category', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}}
            ]
            categories = list(self.db[self.collections['sources']].aggregate(category_pipeline))
            stats['source_categories'] = {item['_id']: item['count'] for item in categories}
            
            # 按核素统计
            nuclide_pipeline = [
                {'$group': {'_id': '$nuclide_name', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}},
                {'$limit': 10}
            ]
            nuclides = list(self.db[self.collections['sources']].aggregate(nuclide_pipeline))
            stats['nuclides'] = {item['_id']: item['count'] for item in nuclides}
            
            logger.info("✅ 统计信息查询成功")
            return stats
            
        except Exception as e:
            logger.error(f"❌ 统计信息查询失败: {e}")
            raise
    
    def clear_all_data(self):
        """清空所有数据（谨慎使用）"""
        try:
            for collection_name in self.collections.values():
                self.db[collection_name].delete_many({})
            
            logger.info("✅ 所有数据已清空")
            
        except Exception as e:
            logger.error(f"❌ 清空数据失败: {e}")
            raise
    
    def export_to_json(self, output_file: str = "mongodb_export.json"):
        """
        导出MongoDB数据到JSON文件
        
        Args:
            output_file: 输出文件名
        """
        try:
            data = self.get_all_units_with_location()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"✅ 数据已导出到: {output_file}")
            
        except Exception as e:
            logger.error(f"❌ 数据导出失败: {e}")
            raise
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
            logger.info("✅ MongoDB连接已关闭")
    
    def clear_collection(self, collection_key: str) -> bool:
        """
        清空指定集合
        
        Args:
            collection_key: 集合键名
            
        Returns:
            操作是否成功
        """
        try:
            if collection_key not in self.collections:
                logger.error(f"❌ 未知的集合键: {collection_key}")
                return False
            
            collection_name = self.collections[collection_key]
            result = self.db[collection_name].delete_many({})
            logger.info(f"🗑️ 清空集合 {collection_name}: 删除了 {result.deleted_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 清空集合失败: {e}")
            return False
    
    def batch_insert_units(self, units_data: List[Dict]) -> bool:
        """
        批量插入单位数据
        
        Args:
            units_data: 单位数据列表
            
        Returns:
            操作是否成功
        """
        try:
            if not units_data:
                logger.warning("⚠️ 没有数据需要插入")
                return False
            
            # 为每条数据添加创建时间戳
            for unit in units_data:
                unit['created_at'] = datetime.now()
                unit['updated_at'] = datetime.now()
            
            collection = self.db[self.collections['units']]
            result = collection.insert_many(units_data)
            
            logger.info(f"✅ 批量插入成功: {len(result.inserted_ids)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 批量插入失败: {e}")
            return False

def main():
    """测试函数"""
    manager = RadiationDataManager()
    
    # 获取统计信息
    stats = manager.get_statistics()
    print("📊 数据库统计信息:")
    print(f"   单位总数: {stats['total_units']}")
    print(f"   放射源总数: {stats['total_sources']}")
    print(f"   已地理编码: {stats['geocoded_units']}")
    
    manager.close_connection()

if __name__ == "__main__":
    main() 