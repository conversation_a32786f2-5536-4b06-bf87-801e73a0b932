#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地理编码脚本
使用高德地图API为地址获取经纬度坐标
"""

import json
import requests
import time
import os
from urllib.parse import quote

# 高德地图API配置
# 请在高德开放平台申请API Key: https://lbs.amap.com/
AMAP_API_KEY = "addee20e44549d9a58a43451785008e8"  # 请替换为您的高德地图API Key
GEOCODING_URL = "https://restapi.amap.com/v3/geocode/geo"

def load_map_data():
    """加载地图数据"""
    with open("radiation_map_data_fixed.json", 'r', encoding='utf-8') as f:
        return json.load(f)

def geocode_address(address, max_retries=3):
    """
    使用高德地图API进行地理编码
    """
    if not address or AMAP_API_KEY == "addee20e44549d9a58a43451785008e8":
        return None, None
    
    params = {
        'key': AMAP_API_KEY,
        'address': address,
        'city': '上海市'
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.get(GEOCODING_URL, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data['status'] == '1' and data['geocodes']:
                location = data['geocodes'][0]['location']
                lng, lat = map(float, location.split(','))
                return lng, lat
            else:
                print(f"地理编码失败: {address} - {data.get('info', '未知错误')}")
                return None, None
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            
        except Exception as e:
            print(f"处理地址时出错: {address} - {e}")
            return None, None
    
    return None, None

def create_demo_coordinates(map_data):
    """
    创建演示用的坐标数据（在没有API Key的情况下）
    基于上海市各区的大概位置生成随机坐标
    """
    
    # 上海各区的大概中心坐标
    district_coords = {
        '黄浦区': [121.473701, 31.231706],
        '徐汇区': [121.436525, 31.188523],
        '长宁区': [121.424624, 31.220367],
        '静安区': [121.448224, 31.249162],
        '普陀区': [121.395514, 31.249776],
        '虹口区': [121.491832, 31.260742],
        '杨浦区': [121.526133, 31.259769],
        '闵行区': [121.381709, 31.112685],
        '宝山区': [121.489934, 31.398896],
        '嘉定区': [121.250333, 31.383524],
        '浦东新区': [121.567706, 31.245944],
        '金山区': [121.330736, 30.724697],
        '松江区': [121.223543, 31.032056],
        '青浦区': [121.113021, 31.151209],
        '奉贤区': [121.458472, 30.912345],
        '崇明区': [121.397516, 31.626946]
    }
    
    import random
    
    for unit in map_data:
        district = unit['district']
        if district in district_coords:
            base_lng, base_lat = district_coords[district]
            # 在区域中心附近随机偏移（约5km范围内）
            lng_offset = random.uniform(-0.05, 0.05)
            lat_offset = random.uniform(-0.05, 0.05)
            
            unit['longitude'] = base_lng + lng_offset
            unit['latitude'] = base_lat + lat_offset
            unit['geocoded'] = True
        else:
            # 默认使用上海市中心坐标
            unit['longitude'] = 121.473701 + random.uniform(-0.1, 0.1)
            unit['latitude'] = 31.231706 + random.uniform(-0.1, 0.1)
            unit['geocoded'] = True
    
    return map_data

def geocode_all_addresses(map_data):
    """
    为所有地址进行地理编码
    """
    total_count = len(map_data)
    success_count = 0
    
    print(f"开始为 {total_count} 个地址进行地理编码...")
    
    if AMAP_API_KEY == "addee20e44549d9a58a43451785008e8":
        print("警告: 未配置高德地图API Key，将使用演示坐标")
        return create_demo_coordinates(map_data)
    
    for i, unit in enumerate(map_data):
        if unit['address']:
            print(f"处理 {i+1}/{total_count}: {unit['name']} - {unit['address']}")
            
            lng, lat = geocode_address(unit['address'])
            
            if lng and lat:
                unit['longitude'] = lng
                unit['latitude'] = lat
                unit['geocoded'] = True
                success_count += 1
            else:
                unit['longitude'] = None
                unit['latitude'] = None
                unit['geocoded'] = False
            
            # API限制: 每秒最多20次请求
            time.sleep(0.1)
        else:
            print(f"跳过 {i+1}/{total_count}: {unit['name']} - 无地址信息")
            unit['geocoded'] = False
    
    print(f"地理编码完成: {success_count}/{total_count} 个地址成功获取坐标")
    return map_data

def save_geocoded_data(map_data):
    """保存地理编码后的数据"""
    
    # 保存完整数据
    with open("radiation_map_data_geocoded.json", 'w', encoding='utf-8') as f:
        json.dump(map_data, f, ensure_ascii=False, indent=2)
    
    print("地理编码后的数据已保存到: radiation_map_data_geocoded.json")
    
    # 创建只包含基本信息的轻量版本（用于地图显示）
    lite_data = []
    for unit in map_data:
        if unit['geocoded']:
            lite_unit = {
                'id': unit['id'],
                'name': unit['name'],
                'address': unit['address'],
                'district': unit['district'],
                'legal_person': unit['legal_person'],
                'legal_phone': unit['legal_phone'],
                'longitude': unit['longitude'],
                'latitude': unit['latitude'],
                'source_count': unit['source_count'],
                'main_nuclides': list(set([s['nuclide_name'] for s in unit['sources'] if s['nuclide_name']]))[:3],
                'categories': list(set([s['source_category'] for s in unit['sources'] if s['source_category']]))
            }
            lite_data.append(lite_unit)
    
    with open("radiation_map_data_lite.json", 'w', encoding='utf-8') as f:
        json.dump(lite_data, f, ensure_ascii=False, indent=2)
    
    print(f"轻量版数据已保存到: radiation_map_data_lite.json ({len(lite_data)} 个有效坐标点)")

def create_api_config_template():
    """创建API配置模板"""
    
    config_content = """# 高德地图API配置

## 获取API Key

1. 访问高德开放平台: https://lbs.amap.com/
2. 注册账号并登录
3. 创建应用，选择"Web服务"类型
4. 获取API Key

## 配置方法

在 geocoding.py 文件中将：
```python
AMAP_API_KEY = "addee20e44549d9a58a43451785008e8"
```

替换为：
```python
AMAP_API_KEY = "addee20e44549d9a58a43451785008e8"
```

## 注意事项

- 个人开发者每日免费额度：5000次
- 推荐使用环境变量存储API Key，避免泄露
- 可以在系统环境变量中设置 AMAP_API_KEY
"""
    
    with open("API_CONFIG.md", 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("API配置说明已保存到: API_CONFIG.md")

def main():
    """主函数"""
    print("开始地理编码处理...")
    
    # 创建API配置说明
    create_api_config_template()
    
    # 加载数据
    map_data = load_map_data()
    
    # 进行地理编码
    geocoded_data = geocode_all_addresses(map_data)
    
    # 保存结果
    save_geocoded_data(geocoded_data)
    
    print("地理编码处理完成！")
    
    # 显示统计信息
    total = len(geocoded_data)
    geocoded = sum(1 for unit in geocoded_data if unit['geocoded'])
    print(f"\n统计信息:")
    print(f"- 总单位数: {total}")
    print(f"- 成功地理编码: {geocoded}")
    print(f"- 成功率: {geocoded/total*100:.1f}%")

if __name__ == "__main__":
    main()